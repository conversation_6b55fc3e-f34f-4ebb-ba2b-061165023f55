import configparser


def load_milvus_config(env):
    """
    根据给定的环境加载配置。

    Args:
        env (str): 要加载的环境名称。

    Returns:
        tuple: 包含数据库配置的元组 (milvus_uri, milvus_user, milvus_password, milvus_db_name)。
    """

    # 从配置文件加载数据库配置
    config = configparser.ConfigParser()
    config.read('config/config.ini')

    section_name = env.lower()
    print(section_name)
    if section_name not in config:
        raise ValueError(f"在配置文件中找不到环境 '{env}'。")

    section = config[section_name]
    milvus_uri = section.get('milvus_uri')
    milvus_user = section.get('milvus_user')
    milvus_password = section.get('milvus_password')
    milvus_db_name = section.get('milvus_db_name')

    return milvus_uri, milvus_user, milvus_password, milvus_db_name
