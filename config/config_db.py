import configparser


def load_config(env):
    """
    根据给定的环境加载配置。

    Args:
        env (str): 要加载的环境名称。

    Returns:
        tuple: 包含数据库配置的元组 (db_host, db_port, db_user, db_password, db_database)。
    """

    # 从配置文件加载数据库配置
    config = configparser.ConfigParser()
    config.read('config/config.ini')

    section_name = env.lower()
    if section_name not in config:
        raise ValueError(f"在配置文件中找不到环境 '{env}'。")

    section = config[section_name]
    db_host = section.get('db_host')
    db_port = section.getint('db_port')
    db_user = section.get('db_user')
    db_password = section.get('db_password')
    db_database = section.get('db_database')

    return db_host, db_port, db_user, db_password, db_database
