import os

# 历史对话轮数
HISTORY_LEN = 3

# 大模型最长支持的长度，如果不填写，则使用模型默认的最大长度，如果填写，则为用户设定的最大长度
MAX_TOKENS = None

# LLM通用对话参数
TEMPERATURE = 0.5
# TOP_P = 0.95 # ChatOpenAI暂不支持该参数

LLM_MODEL = {
    # 线上模型。请在server_config中为每个在线API设置不同的端口

    # 百度千帆
    "qianfan": {
        # "version": "ERNIE-Bot-turbo",  # 注意大小写。当前支持 "ERNIE-Bot" 或 "ERNIE-Bot-turbo"， 更多的见官方文档。
        # "version": "ERNIE-Speed",  # 注意大小写。当前支持 "ERNIE-Bot" 或 "ERNIE-Bot-turbo"， 更多的见官方文档。
        "version": "ERNIE-Speed-128K",  # 注意大小写。当前支持 "ERNIE-Bot" 或 "ERNIE-Bot-turbo"， 更多的见官方文档。
        "api_key": "7Ipu16Ub12oICOLXLG9VLZDk",
        "secret_key": "Qo8YVuvgjyheMFpsehlBA5EFghiXY16E",
    },

    # "openai": {
    #     "model_name": "gpt-3.5-turbo",
    #     "api_base_url": "https://api.openai.com/v1",
    #     "api_key": "",
    #     "openai_proxy": "",
    # },
    #使用openai的sdk兼容deepseek 千帆的大模型
    "openai": {
        "model_name": "deepseek-v3",
        "api_base_url": "https://qianfan.baidubce.com/v2",
        "api_key": "bce-v3/ALTAK-ACzAHOn95iBADeocBa3HY/6efea81fb44354a5577af74564a1977de3511e94",

    },

    # 使用openai的sdk兼容deepseek 阿里的大模型
    # "openai": {
    #     "model_name": "deepseek-v3",
    #     # "model_name": "qwen-max-0125",
    #     # "model_name": "qwen-max-latest",
    #     # "model_name": "qwen-plus",
    #     "api_base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
    #     "api_key": "sk-2fbe0c11bc1245a29940247e33fc83bd",
    #
    # },
    # 使用openai的sdk兼容deepseek 讯飞的大模型
    #https://training.xfyun.cn/modelService

    # "openai": {
    #     "model_name": "xdeepseekv3",
    #     "api_base_url": "https://maas-api.cn-huabei-1.xf-yun.com/v1",
    #     "api_key": "sk-fJ9Y6VzqYdvKCEgSC545DdFf43B741288669Ca40D045B057",
    #
    # },

    # 具体注册及api key获取请前往 http://open.bigmodel.cn
    "zhipu": {
        "api_key": "",
        "version": "chatglm_turbo",  # 可选包括 "chatglm_turbo"
        "provider": "ChatGLMWorker",
    },


    # 具体注册及api key获取请前往 https://xinghuo.xfyun.cn/
    "xinghuo": {
        "APPID": "",
        "APISecret": "",
        "api_key": "",
        "version": "v1.5",  # 你使用的讯飞星火大模型版本，可选包括 "v3.0", "v1.5", "v2.0"
        "provider": "XingHuoWorker",
    },

    # 阿里云通义千问 API，文档参考 https://help.aliyun.com/zh/dashscope/developer-reference/api-details
    "qwen": {
        "version": "qwen-turbo",  # 可选包括 "qwen-turbo", "qwen-plus"
        "api_key": "",  # 请在阿里云控制台模型服务灵积API-KEY管理页面创建
        "provider": "QwenWorker",
    },

    # 百川 API，申请方式请参考 https://www.baichuan-ai.com/home#api-enter
    "baichuan": {
        "version": "Baichuan2-53B",  # 当前支持 "Baichuan2-53B"， 见官方文档。
        "api_key": "",
        "secret_key": "",
        "provider": "BaiChuanWorker",
    },

}
