# config_env.py
import os

# 全局环境变量，可以动态修改
_current_env = None

def get_env_name():
    """获取当前环境名称"""
    global _current_env
    if _current_env:
        return _current_env

    profile = os.getenv("profile")
    # 定义环境名称变量
    # 'ysprod' 为默认环境（企业通用知识库）
    if profile:
        return profile
    else:
        return 'ysprod'

def set_env_name(env):
    """动态设置环境名称（用于测试）"""
    global _current_env
    _current_env = env

# 为了保持向后兼容性
env_name = get_env_name()
