# prompt_config.py
"""
提示词配置管理模块
根据不同环境提供不同的提示词配置
"""

from config.config_env import get_env_name

class PromptConfig:
    """提示词配置类"""

    # 环境配置映射
    ENV_CONFIGS = {
        'ysprod': {
            'name': '企业通用知识库',
            'description': '适用于企业内部各类问题的通用知识库',
            'role': '企业知识管理专家',
            'domain': '企业内部的各类问题，包括但不限于业务流程、制度规范、技术文档、产品信息等',
            'additional_guidance': '充分挖掘和整合提供信息中的有用内容，即使信息片段化也要尽力组织成有意义的回答。'
        },
        'development': {
            'name': '煤炭设备领域知识库',
            'description': '专门针对煤炭设备领域的专业知识库',
            'role': '煤炭设备领域的专家',
            'domain': '煤炭设备',
            'additional_guidance': '充分挖掘和整合提供信息中的有用内容，即使信息片段化也要尽力组织成有意义的回答。'
        },
        'production': {
            'name': '煤炭设备领域知识库',
            'description': '专门针对煤炭设备领域的专业知识库',
            'role': '煤炭设备领域的专家',
            'domain': '煤炭设备',
            'additional_guidance': '充分挖掘和整合提供信息中的有用内容，即使信息片段化也要尽力组织成有意义的回答。'
        }
    }

    @classmethod
    def get_current_config(cls):
        """获取当前环境的配置"""
        current_env = get_env_name()
        return cls.ENV_CONFIGS.get(current_env, cls.ENV_CONFIGS['development'])

    @classmethod
    def get_template(cls):
        """根据当前环境生成提示词模板"""
        config = cls.get_current_config()

        template = f"""
#场景：你作为RAG系统的回答生成模块，严格基于提供的信息为用户生成准确的回答。
#【角色】您是一位经验丰富的{config['role']}，专门帮助解答关于{config['domain']}的问题。

#【用户问题】
{{question}}

#【相关信息】
{{text}}

#【补充信息】
{{graph_answer}}

#【严格回答原则】
1. **信息来源限制**：只能基于上述【相关信息】和【补充信息】中的内容进行回答
2. **禁止外部知识**：不得使用任何未在上述信息中提及的知识、经验或常识
3. **信息处理策略**：
   - 如果【相关信息】和【补充信息】包含答案，请整合这些信息进行回答
   - 如果信息部分相关，请基于相关部分进行回答，并说明信息的局限性
   - 如果信息完全无关或为空，请明确告知无法基于现有信息回答
4. **优先级处理**：【补充信息】优先级高于【相关信息】
5. **友好交互**：对于打招呼、感谢等非查询性输入，可以友好回应

#【回答要求】
- 严格基于提供的信息，不得添加任何外部知识
- 回答要准确、有条理，充分利用现有信息
- 如果信息不足，诚实说明并基于现有信息尽力回答
- 不要在回答中提及"相关信息"、"补充信息"等技术术语
- 语言要自然流畅，避免生硬的表述"""

        # 添加额外的指导（如果有的话）
        if config['additional_guidance']:
            template += f"\n- {config['additional_guidance']}"

        template += "\n\n"

        return template

    @classmethod
    def get_environment_info(cls):
        """获取当前环境信息"""
        current_env = get_env_name()
        config = cls.get_current_config()
        return {
            'env_name': current_env,
            'config_name': config['name'],
            'description': config['description'],
            'role': config['role'],
            'domain': config['domain']
        }
