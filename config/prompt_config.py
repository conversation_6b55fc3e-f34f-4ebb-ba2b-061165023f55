# prompt_config.py
"""
提示词配置管理模块
根据不同环境提供不同的提示词配置
"""

from config.config_env import get_env_name

class PromptConfig:
    """提示词配置类"""

    # 环境配置映射
    ENV_CONFIGS = {
        'ysprod': {
            'name': '企业通用知识库',
            'description': '适用于企业内部各类问题的通用知识库',
            'role': '企业知识管理专家',
            'domain': '企业内部的各类问题，包括但不限于业务流程、制度规范、技术文档、产品信息等',
            'additional_guidance': '回答时要结合企业的实际情况，提供实用性强的建议和解决方案。'
        },
        'development': {
            'name': '煤炭设备领域知识库',
            'description': '专门针对煤炭设备领域的专业知识库',
            'role': '煤炭设备领域的专家',
            'domain': '煤炭设备',
            'additional_guidance': ''
        },
        'production': {
            'name': '煤炭设备领域知识库',
            'description': '专门针对煤炭设备领域的专业知识库',
            'role': '煤炭设备领域的专家',
            'domain': '煤炭设备',
            'additional_guidance': ''
        }
    }

    @classmethod
    def get_current_config(cls):
        """获取当前环境的配置"""
        current_env = get_env_name()
        return cls.ENV_CONFIGS.get(current_env, cls.ENV_CONFIGS['development'])

    @classmethod
    def get_template(cls):
        """根据当前环境生成提示词模板"""
        config = cls.get_current_config()

        template = f"""
#场景：你作为rag流程中的资料整合模块，根据用户问题，【相关信息】【补充信息】中的内容，为用户提供专业的回答。
#【角色】您现在扮演一位经验丰富的{config['role']}，专门帮助解答关于{config['domain']}的问题。请根据以下信息为用户提供一个详细的、专业的回答。确保您的回答准确、有条理。
#【思维链提示】
- 首先，仔细分析用户的问题，判断它是属于打招呼、感谢等非查询性输入还是实质性查询。
- 如果是打招呼或类似的非查询性输入，请直接以礼貌的方式回应用户，忽略【相关信息】【补充信息】中的内容，无需进行进一步的信息检索。
- 如果是实质性查询，请继续按照下面的指导构建专业回答。

#【用户问题】
 ##{{question}}

#【相关信息】
## {{text}}

#【补充信息】
此外，根据已有的知识库，这里还有一些补充信息，可以帮助更全面地理解这个问题：
 {{graph_answer}}

#【生成回答指导】
- 仅根据【相关信息】和【补充信息】中的内容，为用户提供专业的回答,不需要额外的发散,不需要引用其他的资料.
- 如果【相关信息】和【补充信息】中都没有相关的内容，请礼貌地引导用户提供更具体的问题或换个角度询问。
- 如有【补充信息】存在的话，以【补充信息】的内容优先。
- 如果【相关信息】的内容和用户的问题没有直接关联，则可以忽略。
- 信息不足时，不要提及"根据当前信息"、"提供的信息"、"现有信息"等表述。
- 在你的回答中不要提及'相关信息'，'补充信息'、'知识库'等技术术语。
- 回答要自然流畅，就像正常对话一样，避免生硬的表述。
- 无法完整回答时，要礼貌地建议用户提供更详细的背景或换个角度提问。"""

        # 添加额外的指导（如果有的话）
        if config['additional_guidance']:
            template += f"\n- {config['additional_guidance']}"

        template += "\n\n"

        return template

    @classmethod
    def get_environment_info(cls):
        """获取当前环境信息"""
        current_env = get_env_name()
        config = cls.get_current_config()
        return {
            'env_name': current_env,
            'config_name': config['name'],
            'description': config['description'],
            'role': config['role'],
            'domain': config['domain']
        }
