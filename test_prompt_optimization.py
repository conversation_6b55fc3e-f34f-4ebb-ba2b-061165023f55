#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的提示词效果
"""

from server.prompt.prompt_knowledge import get_knowledge_prompt, get_environment_info

def test_prompt_generation():
    """测试提示词生成"""
    print("=== 环境信息 ===")
    env_info = get_environment_info()
    for key, value in env_info.items():
        print(f"{key}: {value}")

    print("\n=== 测试提示词生成 ===")

    # 获取提示词模板
    prompt_template = get_knowledge_prompt()

    # 测试数据
    test_question = "你好，请问如何提高工作效率？"
    test_context = "根据企业管理经验，工作效率的提升需要从时间管理、任务优先级、工具使用等方面入手。"
    test_graph_answer = "时间管理是提高工作效率的关键因素之一。"

    # 生成完整提示词
    full_prompt = prompt_template.format(
        question=test_question,
        text=test_context,
        graph_answer=test_graph_answer
    )

    print("生成的完整提示词:")
    print("-" * 50)
    print(full_prompt)
    print("-" * 50)

    print("\n=== 测试完成 ===")
    print("提示词已修正，主要特点:")
    print("1. 严格基于【相关信息】和【补充信息】回答")
    print("2. 禁止使用外部知识和经验")
    print("3. 充分挖掘和整合现有信息")
    print("4. 信息不足时诚实说明但尽力基于现有信息回答")
    print("5. 保持专业性的同时语言自然流畅")

if __name__ == "__main__":
    test_prompt_generation()
