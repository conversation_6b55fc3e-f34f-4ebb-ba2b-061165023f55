#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的提示词效果
"""

from server.prompt.prompt_knowledge import get_knowledge_prompt, get_environment_info

def test_prompt_generation():
    """测试提示词生成"""
    print("=== 环境信息 ===")
    env_info = get_environment_info()
    for key, value in env_info.items():
        print(f"{key}: {value}")
    
    print("\n=== 测试提示词生成 ===")
    
    # 获取提示词模板
    prompt_template = get_knowledge_prompt()
    
    # 测试数据
    test_question = "你好，请问如何提高工作效率？"
    test_context = "根据企业管理经验，工作效率的提升需要从时间管理、任务优先级、工具使用等方面入手。"
    test_graph_answer = "时间管理是提高工作效率的关键因素之一。"
    
    # 生成完整提示词
    full_prompt = prompt_template.format(
        question=test_question,
        text=test_context,
        graph_answer=test_graph_answer
    )
    
    print("生成的完整提示词:")
    print("-" * 50)
    print(full_prompt)
    print("-" * 50)
    
    print("\n=== 测试完成 ===")
    print("提示词已优化，主要改进:")
    print("1. 更加灵活的信息处理策略")
    print("2. 鼓励基于有限信息给出有价值的建议")
    print("3. 更加实用导向的回答要求")
    print("4. 简化了语言表述，更易理解")

if __name__ == "__main__":
    test_prompt_generation()
