aiohttp==3.9.1
aiolimiter==1.1.0
aiosignal==1.3.1
annotated-types==0.6.0
anyio==4.2.0
async-timeout==4.0.3
attrs==23.2.0
bce-python-sdk==0.9.2
certifi==2023.11.17
charset-normalizer==3.3.2
click==8.1.7
dataclasses-json==0.6.3
exceptiongroup==1.2.0
fastapi==0.109.1
frozenlist==1.4.1
future==0.18.3
h11==0.14.0
idna==3.6
jsonpatch==1.33
jsonpointer==2.4
langchain==0.3.0
langchain-community==0.3.0
langchain-core>=0.3.0,<0.4.0
langgraph==0.2.20
markdown-it-py==3.0.0
marshmallow==3.20.2
mdurl==0.1.2
multidict==6.0.4
mypy-extensions==1.0.0
numpy==1.24.4
packaging==23.2
pip==23.3.2
prompt-toolkit==3.0.43
proto-plus==1.22.3
protobuf==4.25.2
pyarrow==15.0.0
pycryptodome==3.20.0
pydantic===2.7.4
pydantic_core
Pygments==2.17.2
PyMySQL==1.1.0
python-dateutil==2.8.2
python-dotenv==1.0.1
PyYAML==6.0.1
qianfan==********
requests==2.31.0
rich==13.7.0
setuptools==69.0.3
six==1.16.0
sniffio==1.3.0
SQLAlchemy==1.4.51
starlette==0.35.1
tenacity==8.2.3
typer==0.9.0
typing_extensions>=4.9.0
typing-inspect==0.9.0
urllib3==1.26.18
uvicorn==0.26.0
wcwidth==0.2.13
wheel==0.42.0
yarl==1.9.4
sqlglot==22.4.0
docx2txt==0.8
pypdf==4.1.0
unstructured==0.14.3
redis==5.0.0
DBUtils==3.1.0
pymilvus==2.5.0
openpyxl==3.1.5
pandas==2.0.3
beautifulsoup4~=4.12.3
langchain-openai==0.3.0
