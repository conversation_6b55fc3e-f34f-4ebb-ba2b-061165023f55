# `start-cmks-llm.sh` 脚本详解

这个脚本是一个用于启动大模型Agent应用系统的Bash脚本。下面我将详细介绍脚本内容和相关语法知识。

## 1. 脚本概述

脚本主要完成以下任务：
1. 登录阿里云Docker Registry
2. 检查并停止已存在的容器
3. 拉取最新镜像
4. 启动新容器
5. 显示容器状态

## 2. 脚本头部

```bash
#!/bin/bash
```
- 这是一个"shebang"行，指定使用`/bin/bash`解释器执行脚本
- 当脚本被直接执行时（如`./start-cmks-llm.sh`），系统会使用bash来解释执行脚本内容

## 3. 输出信息

```bash
echo "===== 大模型Agent应用系统启动脚本 ====="
echo ""
```
- `echo`命令用于输出文本到标准输出
- 空的`echo`命令输出一个空行，用于格式美化

## 4. 登录Docker Registry

```bash
echo "正在登录阿里云Docker Registry..."
USERNAME="鲁中煤炭交易中心123"
PASSWORD="Q8@lY33sQ-2kM"

echo "$PASSWORD" | docker login --username "$USERNAME" --password-stdin registry.cn-hangzhou.aliyuncs.com
```
- 变量赋值：使用`变量名=值`的形式，注意等号两边不能有空格
- 变量引用：使用`$变量名`或`${变量名}`形式引用变量值
- 管道操作符`|`：将左侧命令的输出作为右侧命令的输入
- `--password-stdin`：从标准输入读取密码，比在命令行中直接提供密码更安全

## 5. 错误处理

```bash
if [ $? -ne 0 ]; then
    echo "Docker登录失败，可能原因："
    echo "1. 用户名或密码不正确"
    echo "2. 账号没有访问该仓库的权限"
    echo "3. 网络连接问题"
    echo ""
    echo "请检查凭据并重试。如果问题持续，请联系管理员。"
    exit 1
fi
```
- `$?`：特殊变量，保存上一个命令的退出状态码（0表示成功，非0表示失败）
- `[ 条件 ]`：测试命令，用于条件判断
- `-ne`：数值比较运算符，表示"不等于"
- `if...then...fi`：条件结构，当条件为真时执行then后的命令
- `exit 1`：以状态码1退出脚本，表示发生错误

## 6. 检查并停止已存在的容器

```bash
echo "检查是否存在旧版容器..."
if docker ps -a | grep -q cmks-llm; then
    echo "发现旧版容器，正在停止并删除..."
    docker rm -f cmks-llm
    if [ $? -ne 0 ]; then
        echo "删除旧容器失败！"
        exit 1
    fi
    echo "旧版容器已成功删除！"
else
    echo "未发现旧版容器，继续执行..."
fi
```
- `docker ps -a`：列出所有容器（包括停止的）
- `grep -q`：静默搜索，只返回退出状态码，不输出匹配行
- `if...then...else...fi`：带else分支的条件结构
- `docker rm -f`：强制删除容器，即使容器正在运行

## 7. 拉取最新镜像

```bash
echo "正在拉取最新镜像..."
docker pull registry.cn-hangzhou.aliyuncs.com/szyktest1/szyk-cmks-llm-ysprod:1.00
if [ $? -ne 0 ]; then
    echo "拉取镜像失败！"
    exit 1
fi
```
- `docker pull`：从镜像仓库拉取镜像
- 镜像命名格式：`registry地址/命名空间/镜像名:标签`

## 8. 启动新容器

```bash
echo "正在启动容器..."
docker run -d -p 8089:8089 -e profile=ysprod --name cmks-llm registry.cn-hangzhou.aliyuncs.com/szyktest1/szyk-cmks-llm-ysprod:1.00
if [ $? -ne 0 ]; then
    echo "启动容器失败！"
    exit 1
fi
```
- `docker run`：创建并启动容器
- `-d`：后台运行容器
- `-p 8089:8089`：端口映射，将主机的8089端口映射到容器的8089端口
- `-e profile=ysprod`：设置环境变量
- `--name cmks-llm`：指定容器名称

## 9. 显示容器状态

```bash
echo "容器状态："
docker ps | grep cmks-llm
```
- `docker ps`：列出运行中的容器
- 使用`grep`过滤出特定容器的信息

## 10. 脚本结束提示

```bash
echo ""
echo "===== 系统已成功启动 ====="
echo "服务地址: http://localhost:8089"
echo ""
```
- 提供服务访问地址信息

## 11. Bash语法知识点总结

1. **变量**
    - 定义：`变量名=值`（等号两边不能有空格）
    - 引用：`$变量名`或`${变量名}`
    - 特殊变量：`$?`（上一命令的退出状态）

2. **条件结构**
    - `if [ 条件 ]; then ... fi`
    - `if [ 条件 ]; then ... else ... fi`
    - `if [ 条件1 ]; then ... elif [ 条件2 ]; then ... else ... fi`

3. **测试命令**
    - 文件测试：`[ -f 文件 ]`（文件存在且是普通文件）
    - 数值比较：`[ $a -eq $b ]`（等于）、`[ $a -ne $b ]`（不等于）
    - 字符串比较：`[ "$a" = "$b" ]`（相等）、`[ -z "$a" ]`（长度为零）

4. **命令执行**
    - 顺序执行：`命令1; 命令2`
    - 条件执行：`命令1 && 命令2`（命令1成功才执行命令2）
    - 管道：`命令1 | 命令2`（命令1的输出作为命令2的输入）

5. **退出状态**
    - `exit 0`：成功退出
    - `exit 非零值`：失败退出，不同的值表示不同的错误类型

这个脚本展示了一个典型的Docker应用部署流程，包含了错误处理、状态检查和用户友好的输出信息，是一个结构良好的Bash脚本示例。
