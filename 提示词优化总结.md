# 提示词优化总结

## 问题分析

用户反馈的问题：AI回复"我无法根据当前提供的信息回答您的问题"，这种表述暴露了技术实现细节，用户体验不佳。

## 核心优化目标

**消除技术术语，让AI回答更自然亲切，就像真人专家在对话。**

## 主要优化内容

### 1. 禁止提及信息来源的表述

**优化前的问题表述：**
- "根据当前提供的信息"
- "基于现有信息"
- "提供的信息不足"
- "无法根据现有信息回答"

**优化后的要求：**
- 信息不足时，不要提及"根据当前信息"、"提供的信息"、"现有信息"等表述
- 在回答中不要提及'相关信息'，'补充信息'、'知识库'等技术术语
- 回答要自然流畅，就像正常对话一样，避免生硬的表述

### 2. 改进无法回答时的处理方式

**优化前：**
```
如果【相关信息】和【补充信息】中都没有相关的内容，直接礼貌的提示用户无法回答。
```

**优化后：**
```
如果【相关信息】和【补充信息】中都没有相关的内容，请礼貌地引导用户提供更具体的问题或换个角度询问。
无法完整回答时，要礼貌地建议用户提供更详细的背景或换个角度提问。
```

### 3. 增强自然对话感

**新增要求：**
- 回答要自然亲切，避免提及信息来源，就像企业内部专家在面对面交流一样
- 回答要自然流畅，就像正常对话一样，避免生硬的表述

## 环境特定优化

### ysprod环境（企业通用知识库）
```
additional_guidance: '回答要自然亲切，避免提及信息来源，就像企业内部专家在面对面交流一样。'
```

### development/production环境（煤炭设备领域）
```
additional_guidance: '回答要自然亲切，避免提及信息来源，就像专业技术人员在现场交流一样。'
```

## 预期效果对比

### 优化前的回答示例：
```
我无法根据当前提供的信息回答您的问题。如果您有关于企业知识管理的具体问题，例如业务流程、制度规范或技术文档等方面的疑问，我很乐意为您提供专业的解答。
```

### 优化后的期望回答：
```
很抱歉，我需要更多具体信息才能为您提供准确的帮助。您能否详细描述一下您遇到的具体情况？或者换个角度提问，比如您希望了解哪个方面的内容？我很乐意为您提供专业的解答。
```

## 核心改进点

1. **消除技术暴露**：不再提及"根据信息"、"知识库"等技术术语
2. **增强引导性**：从"无法回答"改为"引导用户提供更多信息"
3. **提升亲切感**：模拟真人专家面对面交流的语气
4. **保持专业性**：在自然对话的同时保持专业水准

## 严格约束保持

虽然优化了表达方式，但核心约束依然严格：
- ✅ 仍然只能基于【相关信息】和【补充信息】回答
- ✅ 仍然禁止使用外部知识
- ✅ 仍然严格遵循RAG原则
- ✅ 只是改变了表达方式，让用户体验更好

## 测试建议

建议使用相同的测试用例验证优化效果：

```json
{
  "question": "你好，请问如何提高工作效率？",
  "text": "",
  "graph_answer": ""
}
```

**期望的优化后回答：**
应该是引导性的、自然的回复，而不是生硬地提及"无法根据当前信息回答"。

## 部署状态

✅ 优化已完成，立即生效，无需重启服务。

现在AI助手会像真正的企业专家一样自然地与用户对话，在信息不足时会礼貌地引导用户提供更多信息，而不是生硬地拒绝回答。
