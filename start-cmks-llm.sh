#!/bin/bash

echo "===== 大模型Agent应用系统启动脚本 ====="
echo ""

# 登录阿里云Docker Registry（使用更安全的方式提供密码）
echo "正在登录阿里云Docker Registry..."
USERNAME="鲁中煤炭交易中心123"
PASSWORD="Q8@lY33sQ-2kM"

# 使用echo和管道传递密码，避免在命令行中明文显示
echo "$PASSWORD" | docker login --username "$USERNAME" --password-stdin registry.cn-hangzhou.aliyuncs.com

# 检查登录状态
if [ $? -ne 0 ]; then
    echo "Docker登录失败，可能原因："
    echo "1. 用户名或密码不正确"
    echo "2. 账号没有访问该仓库的权限"
    echo "3. 网络连接问题"
    echo ""
    echo "请检查凭据并重试。如果问题持续，请联系管理员。"
    exit 1
fi
echo "Docker登录成功！"
echo ""

# 检查并停止已存在的容器
echo "检查是否存在旧版容器..."
if docker ps -a | grep -q cmks-llm; then
    echo "发现旧版容器，正在停止并删除..."
    docker rm -f cmks-llm
    if [ $? -ne 0 ]; then
        echo "删除旧容器失败！"
        exit 1
    fi
    echo "旧版容器已成功删除！"
else
    echo "未发现旧版容器，继续执行..."
fi
echo ""

# 拉取最新镜像
echo "正在拉取最新镜像..."
docker pull registry.cn-hangzhou.aliyuncs.com/szyktest1/szyk-cmks-llm-ysprod:1.00
if [ $? -ne 0 ]; then
    echo "拉取镜像失败！"
    exit 1
fi
echo "镜像拉取成功！"
echo ""

# 启动新容器
echo "正在启动容器..."
docker run -d -p 8089:8089 -e profile=ysprod --name cmks-llm registry.cn-hangzhou.aliyuncs.com/szyktest1/szyk-cmks-llm-ysprod:1.00
if [ $? -ne 0 ]; then
    echo "启动容器失败！"
    exit 1
fi
echo "容器启动成功！"
echo ""

# 显示容器状态
echo "容器状态："
docker ps | grep cmks-llm

echo ""
echo "===== 系统已成功启动 ====="
echo "服务地址: http://localhost:8089"
echo ""
