Metadata-Version: 2.1
Name: jsonpatch
Version: 1.33
Summary: Apply <PERSON><PERSON><PERSON>-<PERSON><PERSON> (RFC 6902) 
Home-page: https://github.com/stefankoegl/python-json-patch
Author: <PERSON>
Author-email: <EMAIL>
License: Modified BSD License
Project-URL: Website, https://github.com/stefankoegl/python-json-patch
Project-URL: Repository, https://github.com/stefankoegl/python-json-patch.git
Project-URL: Documentation, https://python-json-patch.readthedocs.org/
Project-URL: PyPI, https://pypi.org/pypi/jsonpatch
Project-URL: Tests, https://travis-ci.org/stefankoegl/python-json-patch
Project-URL: Test Coverage, https://coveralls.io/r/stefankoegl/python-json-patch
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Utilities
Requires-Python: >=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*, !=3.4.*, !=3.5.*, !=3.6.*
License-File: LICENSE
License-File: AUTHORS
Requires-Dist: jsonpointer (>=1.9)

python-json-patch
=================

[![PyPI version](https://img.shields.io/pypi/v/jsonpatch.svg)](https://pypi.python.org/pypi/jsonpatch/)
[![Supported Python versions](https://img.shields.io/pypi/pyversions/jsonpatch.svg)](https://pypi.python.org/pypi/jsonpatch/)
[![Build Status](https://travis-ci.org/stefankoegl/python-json-patch.png?branch=master)](https://travis-ci.org/stefankoegl/python-json-patch)
[![Coverage Status](https://coveralls.io/repos/stefankoegl/python-json-patch/badge.png?branch=master)](https://coveralls.io/r/stefankoegl/python-json-patch?branch=master)

Applying JSON Patches in Python
-------------------------------

Library to apply JSON Patches according to
[RFC 6902](http://tools.ietf.org/html/rfc6902)

See source code for examples

* Website: https://github.com/stefankoegl/python-json-patch
* Repository: https://github.com/stefankoegl/python-json-patch.git
* Documentation: https://python-json-patch.readthedocs.org/
* PyPI: https://pypi.python.org/pypi/jsonpatch
* Travis CI: https://travis-ci.org/stefankoegl/python-json-patch
* Coveralls: https://coveralls.io/r/stefankoegl/python-json-patch

Running external tests
----------------------
To run external tests (such as those from https://github.com/json-patch/json-patch-tests) use ext_test.py

    ./ext_tests.py ../json-patch-tests/tests.json


