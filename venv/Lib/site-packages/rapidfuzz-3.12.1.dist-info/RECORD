rapidfuzz-3.12.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
rapidfuzz-3.12.1.dist-info/METADATA,sha256=k-UhlFRiuKBrfQZGMWWGO8bqiCQxdZaNVfLb6jlRBNY,11961
rapidfuzz-3.12.1.dist-info/RECORD,,
rapidfuzz-3.12.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
rapidfuzz-3.12.1.dist-info/WHEEL,sha256=yxQ6vqLV6BGCdqSh8_cFSPC6ooSNYEn3BaaiIZLFM6w,106
rapidfuzz-3.12.1.dist-info/entry_points.txt,sha256=j5_TXjLHLN9ULqtgOxy8V2w8yS2mzVnowf7vZO7y1uw,121
rapidfuzz-3.12.1.dist-info/licenses/LICENSE,sha256=80zN_9_1b8QXeFkV7t8YYB1UeXVd88RyssdUwpgC1oM,1092
rapidfuzz/__init__.py,sha256=UrC1ir6qF4cUz-cEfilitARu78T-hNR7kEKSf-IWPEY,836
rapidfuzz/__init__.pyi,sha256=6NNoCowtWGbHBRHZ-KPumXXSpk2x4trPmStDWqk9Oug,201
rapidfuzz/__pycache__/__init__.cpython-310.pyc,,
rapidfuzz/__pycache__/_common_py.cpython-310.pyc,,
rapidfuzz/__pycache__/_feature_detector.cpython-310.pyc,,
rapidfuzz/__pycache__/_utils.cpython-310.pyc,,
rapidfuzz/__pycache__/fuzz.cpython-310.pyc,,
rapidfuzz/__pycache__/fuzz_py.cpython-310.pyc,,
rapidfuzz/__pycache__/process.cpython-310.pyc,,
rapidfuzz/__pycache__/process_cpp.cpython-310.pyc,,
rapidfuzz/__pycache__/process_py.cpython-310.pyc,,
rapidfuzz/__pycache__/utils.cpython-310.pyc,,
rapidfuzz/__pycache__/utils_py.cpython-310.pyc,,
rapidfuzz/__pyinstaller/__init__.py,sha256=23yK5Mr7W7x260Wk4_jCzumLdKVCNW_jC0f857cEsco,131
rapidfuzz/__pyinstaller/__pycache__/__init__.cpython-310.pyc,,
rapidfuzz/__pyinstaller/__pycache__/test_rapidfuzz_packaging.cpython-310.pyc,,
rapidfuzz/__pyinstaller/test_rapidfuzz_packaging.py,sha256=iBssItCNdVZpr7JWDJt5z7dIUsydlruuPs2qeLpFg5k,1114
rapidfuzz/_common_py.py,sha256=crDYKpH5lTgYba1J41GTRkpRbmYPqi5zizfr48DeeD4,1766
rapidfuzz/_feature_detector.py,sha256=vTnfTB-Jfljbrupxsvsa5AQcZAkGHg7FXBg0pI3_YVg,332
rapidfuzz/_feature_detector_cpp.cp310-win_amd64.pyd,sha256=w-ugTuWoCGIsZ8mqA9_f5_esmMU_nJEWF2pjwV1qLA8,35840
rapidfuzz/_utils.py,sha256=l-q6M5jMkJT_MqqYQak-k3lE2CAZruQRWiwvfT53_To,2342
rapidfuzz/distance/DamerauLevenshtein.py,sha256=LicvoaIad4CawQuKnG7UJPgNV7yL20lNXfj1CkLtmkg,4055
rapidfuzz/distance/DamerauLevenshtein.pyi,sha256=bMR0Hm2512jXdcBXxTfPQOZwnJonCIY8X2RJfkXsEzg,1904
rapidfuzz/distance/DamerauLevenshtein_py.py,sha256=-zdMM4vv70Dk5VHAhbjeB5TpOd6-Y0bFS0uYbSBB9Fk,6591
rapidfuzz/distance/Hamming.py,sha256=_dj9-1ubfqtKCA2h7JtzZa9NfhIKfbfStNerCuwvSGA,4392
rapidfuzz/distance/Hamming.pyi,sha256=TjoXt9lysM9-ZOedijCii50xGGdjGlz239LWx0wbYeY,2769
rapidfuzz/distance/Hamming_py.py,sha256=FAeokMCOcW3Ub8XlO6tsHWWpdJWSUKVbJCNZCnP-pmI,9081
rapidfuzz/distance/Indel.py,sha256=C-ZCyD3dOamKIvt1ltNDroKPSqbMh4zC4FaudhdvHko,4296
rapidfuzz/distance/Indel.pyi,sha256=xjs1sdft7AcbHOh9rp9pH0FamAf_TrN2qyWBdMXk-NE,2593
rapidfuzz/distance/Indel_py.py,sha256=oE-hmb92o-a5xYU3bj6UQtvUljJ4Cf6VYjXAIwStzWA,9935
rapidfuzz/distance/Jaro.py,sha256=kjB_r6CBVJfUjRiCiRkDrtdloVQ9ak2hUQB44nIlvN0,3575
rapidfuzz/distance/Jaro.pyi,sha256=IpjLgvh73jalJiMMNqwc6TSmk7U9yE_JXLP3Q5605GM,1908
rapidfuzz/distance/JaroWinkler.py,sha256=3RRZ4BNt9Rs4KALgQNYsp8HCBZ4BdMxMOcirnMGwxvo,3831
rapidfuzz/distance/JaroWinkler.pyi,sha256=5XC-Y5fYFnmINM0Dw29BgRUvzFfnG07HVruCrwL4MF8,2164
rapidfuzz/distance/JaroWinkler_py.py,sha256=CJA9ImEQPGqRToXgjejdUES93L5JRZf9gGkbVuX6AbU,6481
rapidfuzz/distance/Jaro_py.py,sha256=1mexVsftTGRzhjctfUHuaG3D8M8T9oADuO9CqKoileU,7345
rapidfuzz/distance/LCSseq.py,sha256=tg62FG8PzPxYGdhx428xUfcd8cgEalB_hmqxc5K-Zcw,4392
rapidfuzz/distance/LCSseq.pyi,sha256=xjs1sdft7AcbHOh9rp9pH0FamAf_TrN2qyWBdMXk-NE,2593
rapidfuzz/distance/LCSseq_py.py,sha256=mH3JXKvXllXX3Ts6daGfdDKGWo5V1xHkI9RyWGVVw5E,11504
rapidfuzz/distance/Levenshtein.py,sha256=UJGLcs9dXL25kOjbi6TOCUHXf6uGMlGTWSotxRXf3JU,4584
rapidfuzz/distance/Levenshtein.pyi,sha256=7C-nWn_yEORoj-Ioow0BsmaLnpKH7ej_5smykFtwcnM,3678
rapidfuzz/distance/Levenshtein_py.py,sha256=f4gag2BCACSGXPVe0gnGnYf5CDLAdyqhvf5cUl9ptRU,17103
rapidfuzz/distance/OSA.py,sha256=DzkSlRrEThCf8zc9OCI5tgNqGAzusWcn5cYE2RAVK64,3543
rapidfuzz/distance/OSA.pyi,sha256=bMR0Hm2512jXdcBXxTfPQOZwnJonCIY8X2RJfkXsEzg,1904
rapidfuzz/distance/OSA_py.py,sha256=BGD49wAjNbA5xqhrUID4vkA7yaeJ3jXljsE60SWbEig,6251
rapidfuzz/distance/Postfix.py,sha256=QoTlbLgjj61CDM_rycFDoap3gARRWXOu6vwIw41Y0QE,3671
rapidfuzz/distance/Postfix.pyi,sha256=bMR0Hm2512jXdcBXxTfPQOZwnJonCIY8X2RJfkXsEzg,1904
rapidfuzz/distance/Postfix_py.py,sha256=ywWO0M5swCWkjZqjp2wKMEZ7_GV8Z91IRMe8_zZGeaQ,5039
rapidfuzz/distance/Prefix.py,sha256=2Iu577WnaYCHCQd8it76gDk60QxRdlFo0cQBItCZzWw,3639
rapidfuzz/distance/Prefix.pyi,sha256=bMR0Hm2512jXdcBXxTfPQOZwnJonCIY8X2RJfkXsEzg,1904
rapidfuzz/distance/Prefix_py.py,sha256=mHtW3wRD0jol76NnDOgIALMvslINtGJJ9ipZ713sldQ,5015
rapidfuzz/distance/__init__.py,sha256=4DnHc3o3Bs6xUyFJTQKqPgenKSuk3Wx80IcUuzRgp0U,621
rapidfuzz/distance/__init__.pyi,sha256=wX594ohPZ4gASmvKpbovDWH2BB9IcVojRqtF0yPjzvg,571
rapidfuzz/distance/__pycache__/DamerauLevenshtein.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/DamerauLevenshtein_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Hamming.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Hamming_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Indel.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Indel_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Jaro.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/JaroWinkler.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/JaroWinkler_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Jaro_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/LCSseq.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/LCSseq_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Levenshtein.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Levenshtein_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/OSA.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/OSA_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Postfix.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Postfix_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Prefix.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/Prefix_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/__init__.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/_initialize.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/_initialize_py.cpython-310.pyc,,
rapidfuzz/distance/__pycache__/metrics_py.cpython-310.pyc,,
rapidfuzz/distance/_initialize.py,sha256=UKwKITEVDTvj7MiWNsmPJBDN7xgowVwlzKllEjDzpoA,3074
rapidfuzz/distance/_initialize.pyi,sha256=N7ooznQtrQhZKNZ1UGtjUyf6LiP_vbtSCWFa49K9fJA,3998
rapidfuzz/distance/_initialize_cpp.cp310-win_amd64.pyd,sha256=2nEH0nFXiQQ663mMubGalrEpi_MZbUaut1XneZWCaw8,273920
rapidfuzz/distance/_initialize_py.py,sha256=I_z-54avD84EE1HA7KjT8and4GHOQI-o2tK9ZG6eYsI,26320
rapidfuzz/distance/metrics_cpp.cp310-win_amd64.pyd,sha256=WXJE4dBgjRv0OhgwY5XrzH0bwYKmvVLrSijqu4FBrts,1622016
rapidfuzz/distance/metrics_cpp_avx2.cp310-win_amd64.pyd,sha256=0ASaaNwnMRQ4rQQEbSH5wvIrR_Wgvlzm9ch80fCHnOw,1617408
rapidfuzz/distance/metrics_py.py,sha256=qtm2UeWOXEAmVUPnxsSE2COrd9KuEArOKTlqQI2qqtk,9037
rapidfuzz/fuzz.py,sha256=gkOM8b_kJ5yaq1er_sAqXJP6wbZJon9msV2L6EwGS24,4625
rapidfuzz/fuzz.pyi,sha256=8I3AJtX50VlobaAgFtqiXg1-pbzaRA9dFSKezv7zgUU,4811
rapidfuzz/fuzz_cpp.cp310-win_amd64.pyd,sha256=Nzxd9z0zspBl2ATvW8qIZRPTRhqa1k1LWEmQz9yJQTg,865792
rapidfuzz/fuzz_cpp_avx2.cp310-win_amd64.pyd,sha256=jr2h8RxNvgHwKEGEImDplVmAiDCTK67gdl2FnbfWiK4,871936
rapidfuzz/fuzz_py.py,sha256=JVwHUd6dXBsqrszsHV6mPd6b1k3BPaeFVSjQ4TVAE7I,25825
rapidfuzz/process.py,sha256=JylT-tYBhwP566p0zO-O2ZT0p-0kW3cCUgbnCCOoVcQ,2672
rapidfuzz/process.pyi,sha256=XMgcnq_61478JDIlBg5lNoZHxqykjomZ3cE9pVIz6p8,17094
rapidfuzz/process_cpp.py,sha256=65ia0dJkd6aGKySmJucFHUCmQdWEqxCkKTrb-zEMuN8,2423
rapidfuzz/process_cpp_impl.cp310-win_amd64.pyd,sha256=--ahZjvdfv_kP8gDQB1QGEAHGT7DWReN0Fy6o5ZQitA,553472
rapidfuzz/process_py.py,sha256=E7ms6eyc8R2_2yQljmm5FBfmoMuPnTux4IGXh4Atn60,26663
rapidfuzz/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
rapidfuzz/utils.py,sha256=7rs2NCCSL2_mandvxSEkcly5WWb0RR4gxKOdYZJiavE,1910
rapidfuzz/utils.pyi,sha256=rz2lxhxQu-Bm6gPxBDjwBbNs2oBBjArP6PEYlWcaE5Q,304
rapidfuzz/utils_cpp.cp310-win_amd64.pyd,sha256=3Yfkvv2nYwojln0jh0WNmU6t_Bj5F1DLg2zzWnnHC3A,165888
rapidfuzz/utils_py.py,sha256=oP6xgOsopkR0NhNjf3u6x55gw03LUMJ1zh2FYIsxno4,622
