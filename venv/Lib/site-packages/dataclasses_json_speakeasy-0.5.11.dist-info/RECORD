dataclasses_json/__init__.py,sha256=O2-gxxDqFvB8F3WyvjBEn3J6epRHaRZjhJtVEhVJfCc,495
dataclasses_json/__pycache__/__init__.cpython-310.pyc,,
dataclasses_json/__pycache__/__version__.cpython-310.pyc,,
dataclasses_json/__pycache__/api.cpython-310.pyc,,
dataclasses_json/__pycache__/cfg.cpython-310.pyc,,
dataclasses_json/__pycache__/core.cpython-310.pyc,,
dataclasses_json/__pycache__/mm.cpython-310.pyc,,
dataclasses_json/__pycache__/stringcase.cpython-310.pyc,,
dataclasses_json/__pycache__/undefined.cpython-310.pyc,,
dataclasses_json/__pycache__/utils.cpython-310.pyc,,
dataclasses_json/__version__.py,sha256=znkSTDEkd3wqaVOJRnT99wHd4F6gd7ojiMZYZCMwU10,154
dataclasses_json/api.py,sha256=hzH18PZWgBifuvqJFdUed7daRqOfCaGUyS2EMfmLXo8,5990
dataclasses_json/cfg.py,sha256=IRREFI4oOTlWENXbOzp86X3_3tRpER-3bXlkBoMqqyk,3527
dataclasses_json/core.py,sha256=uwMnARjhpj3vaQ4WjiaVkQBCkud2ONXdZCRrEdduWK4,17887
dataclasses_json/mm.py,sha256=TKwSIn2OXeHF2E4n_Uhc5Mhe1LBQAKq0nmo5D4hc_qQ,15997
dataclasses_json/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dataclasses_json/stringcase.py,sha256=lCTTNBaXwgudBdL2DAq42L4-yJgiEtWkLD6O5NvXAeo,3313
dataclasses_json/undefined.py,sha256=GaN_LCwHpsfzVqAR2uv7AZyU2ID5tqLVaof81Y9VjM0,10452
dataclasses_json/utils.py,sha256=x4tA6yCJJfyDIB1-wgeXEEAkQLm19f3vydlDv6CM9cg,5714
dataclasses_json_speakeasy-0.5.11.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
dataclasses_json_speakeasy-0.5.11.dist-info/LICENSE,sha256=UjIbVWleiRm3xlSx6Ag8V88hK4hfQocb1sPN0t0mfw8,1084
dataclasses_json_speakeasy-0.5.11.dist-info/METADATA,sha256=hId_Ik23qMcgBexGTY-vM-8mYNoQjm95CwqcwnxWkdQ,25046
dataclasses_json_speakeasy-0.5.11.dist-info/RECORD,,
dataclasses_json_speakeasy-0.5.11.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dataclasses_json_speakeasy-0.5.11.dist-info/WHEEL,sha256=FMvqSimYX_P7y0a7UY-_Mc83r5zkBZsCYPm7Lr0Bsq4,88
