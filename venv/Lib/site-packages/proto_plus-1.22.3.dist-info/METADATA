Metadata-Version: 2.1
Name: proto-plus
Version: 1.22.3
Summary: Beautiful, Pythonic protocol buffers.
Home-page: https://github.com/googleapis/proto-plus-python.git
Author: Google LLC
Author-email: <EMAIL>
License: Apache 2.0
Platform: Posix; MacOS X
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: POSIX
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Software Development :: Code Generators
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.6
License-File: LICENSE
Requires-Dist: protobuf (<5.0.0dev,>=3.19.0)
Provides-Extra: testing
Requires-Dist: google-api-core[grpc] (>=1.31.5) ; extra == 'testing'

Proto Plus for Python
=====================

|pypi| |release level| |docs|

    Beautiful, Pythonic protocol buffers.

This is a wrapper around `protocol buffers`_. Protocol buffers is a
specification format for APIs, such as those inside Google.
This library provides protocol buffer message classes and objects that
largely behave like native Python types.

.. _protocol buffers: https://developers.google.com/protocol-buffers/


Documentation
-------------

`Documentation`_ is available on Read the Docs.

.. _documentation: https://proto-plus-python.readthedocs.io/en/latest/

.. |pypi| image:: https://img.shields.io/pypi/v/proto-plus.svg
   :target: https://pypi.org/project/proto-plus
.. |release level| image:: https://img.shields.io/badge/release%20level-ga-gold.svg?style&#x3D;flat
  :target: https://cloud.google.com/terms/launch-stages
.. |docs| image:: https://readthedocs.org/projects/proto-plus-python/badge/?version=latest
  :target: https://proto-plus-python.readthedocs.io/en/latest/
