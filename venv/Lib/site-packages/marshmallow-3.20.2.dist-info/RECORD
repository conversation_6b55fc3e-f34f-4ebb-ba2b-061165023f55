marshmallow-3.20.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
marshmallow-3.20.2.dist-info/LICENSE,sha256=grsRP99dmM8-9O31HklhEeEoGLP1OSMzPXjFpdtnLhA,1069
marshmallow-3.20.2.dist-info/METADATA,sha256=VyYC5xPeCr1hTMHb-BESDvKwpOMS99IzuRlEvcKYu2Y,7488
marshmallow-3.20.2.dist-info/RECORD,,
marshmallow-3.20.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
marshmallow-3.20.2.dist-info/WHEEL,sha256=oiQVh_5PnQM0E3gPdiz09WCNmwiHDMaGer_elqB3coM,92
marshmallow-3.20.2.dist-info/top_level.txt,sha256=B_yjxTA7Lx7T1LxnaUpTTd8CXt4shc4blEcScke_vVc,12
marshmallow/__init__.py,sha256=OZm2C_79jM_inW-CXpf8oTeDLaXA2jK43bVEARgp26Y,968
marshmallow/__pycache__/__init__.cpython-310.pyc,,
marshmallow/__pycache__/base.cpython-310.pyc,,
marshmallow/__pycache__/class_registry.cpython-310.pyc,,
marshmallow/__pycache__/decorators.cpython-310.pyc,,
marshmallow/__pycache__/error_store.cpython-310.pyc,,
marshmallow/__pycache__/exceptions.cpython-310.pyc,,
marshmallow/__pycache__/fields.cpython-310.pyc,,
marshmallow/__pycache__/orderedset.cpython-310.pyc,,
marshmallow/__pycache__/schema.cpython-310.pyc,,
marshmallow/__pycache__/types.cpython-310.pyc,,
marshmallow/__pycache__/utils.cpython-310.pyc,,
marshmallow/__pycache__/validate.cpython-310.pyc,,
marshmallow/__pycache__/warnings.cpython-310.pyc,,
marshmallow/base.py,sha256=5shImdVXepCDdr-WQ6uyP39K9gNoG8F8sv1pmfsS-W4,1344
marshmallow/class_registry.py,sha256=7C1DLdD-2D7ZKf-ZlfOV3bDrxT7Er7-qQI7M-bYHyac,2824
marshmallow/decorators.py,sha256=X9vNZgfrk3JvhHrVV9CPQxy2XMtGwewVwdvEeX_4u_o,8250
marshmallow/error_store.py,sha256=A7AxgLMw9ffSmaxRH4x3wcBWibx-DuGH4LwSDpVn50I,2223
marshmallow/exceptions.py,sha256=KrJEamfdoFj9zwmT8mtwONJlyHV4hpzZhkn9iHs2t_0,2326
marshmallow/fields.py,sha256=YuODki4r-LSe40pnftxddWT_VHiVRPIo-V5neydfpPs,73258
marshmallow/orderedset.py,sha256=C2aAG6w1faIL1phinbAltbe3AUAnF5MN6n7fzESNDhI,2922
marshmallow/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
marshmallow/schema.py,sha256=61UK2cEiM8fEOJZckqe43Km6tdCxGm65MmLiqao3qVs,49091
marshmallow/types.py,sha256=m1IhBT7dWHrYjTm7BQigHGX8UoYyt32WHwDK5Jppxf0,331
marshmallow/utils.py,sha256=hemvvZB8GxndkC_Zx0ldXqWfYUtJWKpYatzNPhNGRj4,11883
marshmallow/validate.py,sha256=F6r-rGurleYhndgAW_pZfS6xe2KxtST1915RXL-AI7c,23938
marshmallow/warnings.py,sha256=vHQu7AluuWqLhvlw5noXtWWbya13zDXY6JMaVSUzmDs,65
