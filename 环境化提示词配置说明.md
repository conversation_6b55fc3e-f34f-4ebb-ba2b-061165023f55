# 环境化提示词配置功能说明

## 概述

本功能实现了根据不同环境使用不同提示词的需求，特别是将 ysprod 环境的提示词从煤炭设备领域改为企业通用知识库。

## 功能特性

### 1. 多环境支持
- **ysprod**: 企业通用知识库环境
- **development**: 煤炭设备领域开发环境  
- **production**: 煤炭设备领域生产环境

### 2. 动态环境切换
- 支持运行时动态切换环境配置
- 提供API接口进行环境切换和查询

### 3. 统一配置管理
- 所有环境配置集中在 `config/prompt_config.py` 中管理
- 易于维护和扩展新环境

## 文件结构

```
├── config/
│   ├── config_env.py          # 环境配置管理
│   └── prompt_config.py       # 提示词配置管理
├── server/
│   ├── api/knowledge/views.py # 知识库API接口
│   ├── prompt/prompt_knowledge.py # 提示词模块
│   └── service/file_embedding.py # 服务层
```

## API接口

### 1. 获取环境信息
```
GET /api/v1/knowledge/environment_info
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "env_name": "ysprod",
    "config_name": "企业通用知识库",
    "description": "适用于企业内部各类问题的通用知识库",
    "role": "企业知识管理专家",
    "domain": "企业内部的各类问题，包括但不限于业务流程、制度规范、技术文档、产品信息等"
  },
  "message": "环境配置信息获取成功"
}
```

### 2. 切换环境配置
```
POST /api/v1/knowledge/switch_environment
```

**请求体:**
```json
{
  "env_name": "ysprod"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "env_name": "ysprod",
    "config_name": "企业通用知识库",
    "description": "适用于企业内部各类问题的通用知识库",
    "role": "企业知识管理专家",
    "domain": "企业内部的各类问题，包括但不限于业务流程、制度规范、技术文档、产品信息等"
  },
  "message": "环境已成功切换到: ysprod"
}
```

### 3. 知识库问答（原有接口，现在支持环境化提示词）
```
POST /api/v1/knowledge/answer_stream
```

## 环境配置

### 设置环境变量
```bash
# 设置为企业通用知识库环境
export profile=ysprod

# 设置为开发环境
export profile=development

# 设置为生产环境
export profile=production
```

### 默认环境
如果未设置 `profile` 环境变量，系统默认使用 `ysprod` 环境。

## 提示词差异

### ysprod 环境（企业通用知识库）
- **角色**: 企业知识管理专家
- **专业领域**: 企业内部的各类问题，包括但不限于业务流程、制度规范、技术文档、产品信息等
- **特殊指导**: 回答时要结合企业的实际情况，提供实用性强的建议和解决方案

### development/production 环境（煤炭设备领域）
- **角色**: 煤炭设备领域的专家
- **专业领域**: 煤炭设备
- **特殊指导**: 无额外指导

## 使用示例

### 1. 查看当前环境
```bash
curl -X GET "http://127.0.0.1:8089/api/v1/knowledge/environment_info"
```

### 2. 切换到企业通用知识库环境
```bash
curl -X POST "http://127.0.0.1:8089/api/v1/knowledge/switch_environment" \
  -H "Content-Type: application/json" \
  -d '{"env_name": "ysprod"}'
```

### 3. 切换到煤炭设备环境
```bash
curl -X POST "http://127.0.0.1:8089/api/v1/knowledge/switch_environment" \
  -H "Content-Type: application/json" \
  -d '{"env_name": "development"}'
```

### 4. 测试知识库问答
```bash
curl -X POST "http://127.0.0.1:8089/api/v1/knowledge/answer_stream" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "如何提高工作效率？",
    "timestamp": "2024-01-01 12:00:00",
    "tenant_id": "test_tenant"
  }'
```

## 扩展新环境

如需添加新环境，只需在 `config/prompt_config.py` 的 `ENV_CONFIGS` 中添加新的配置项：

```python
ENV_CONFIGS = {
    'new_env': {
        'name': '新环境名称',
        'description': '新环境描述',
        'role': '专家角色',
        'domain': '专业领域',
        'additional_guidance': '额外指导（可选）'
    }
}
```

## 注意事项

1. 环境切换是全局的，会影响所有后续的知识库问答请求
2. 重启应用后环境会恢复到默认设置（除非设置了环境变量）
3. 生产环境建议通过环境变量而非API接口来设置环境
4. 环境切换接口主要用于开发和测试阶段

## 日志输出

在知识库问答过程中，系统会输出详细的环境信息和提示词内容，便于调试：

```
=== 环境信息 ===
环境名称: ysprod
配置名称: 企业通用知识库
角色定位: 企业知识管理专家
专业领域: 企业内部的各类问题，包括但不限于业务流程、制度规范、技术文档、产品信息等
=== 提示词 ===
[具体的提示词内容]
=== 提示词结束 ===
```
