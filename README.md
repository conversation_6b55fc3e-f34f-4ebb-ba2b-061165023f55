# 大模型Agent应用系统

  此工程为Agent应用系统，使用Langchain框架，结合文心千帆大模型，通过fastApi提供接口，实现对大模型的调用。
  
## 框架
- Langchain
- 文心千帆
- dockerfile
- fastApi

## 工程
- config 配置文件
- doc 说明文档
- example Langchain Demo
- logs 日志输出路径
- server 
  - api 对外接口
  - models 模型封装
  - parser 解析器
  - prompt 提示词
  - service 服务
- app.py 系统入口
- dockerfile
- README.md
- requirements.txt
- sys_log.py
- test.py
# swagger
http://127.0.0.1:8089/docs