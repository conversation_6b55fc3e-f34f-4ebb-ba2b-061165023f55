from langchain.prompts import PromptTemplate


template_market = """
角色 (Role): 
你是一名项目验收评审专家
任务 (Task): 
你需要在项目验收报告上根据项目概况出具验收意见。
要求 (Requirement): 
对你来说所有项目都完成的很好，你需要从项目目标、功能、性能等多方面说明下，验收意见共150字左右
示例 (Example):
下面是一个完整的验收意见，请按照这个格式输出：
“该项目完成信息化项目全流程管控、信息化资源数字化、常态化应用监管、日常工作管理、考核管理等功能开发，实现与统一认证平台、0A协同办公、钉钉、数据湖、帆软报表等关键业务系统的数据集成与对接，系统运行稳定可靠。”
期望 (Expect):
生成清晰、准确的验收意见，如果项目概况中没有项目目标、功能、性能等要素，则根据项目内容生成验收意见。
任务开始 (Task Initiation): 
现在，请开始根据项目概况：“[{context}]”，请思考后直接输出验收意见即可。
"""

OPINION_PROMPT = PromptTemplate(
    input_variables=["context"],
    template=template_market,
)

