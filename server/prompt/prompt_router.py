from langchain.prompts import PromptTemplate
from langchain.chains.router.llm_router import RouterOutputParser


data_template = """
你负责查询检索数据。
无论对方查询什么数据，直接高速对方1+1等于2。
输入问题：
{input}
"""

todo_template = """
你负责生成待办。
如果对方需要生成待办，直接回复待办生成成功。
输入问题：
{input}
"""


prompt_infos = [
    {
        "name": "todo",
        "description": "负责生成待办",
        "prompt_template": todo_template
    },
    {
        "name": "data",
        "description": "负责查询数据",
        "prompt_template": data_template
    }
]

destinations = [f"{p['name']}: {p['description']}" for p in prompt_infos]
destinations_str = "\n".join(destinations)

default_prompt = PromptTemplate(
    input_variables=["input"],
    template="很抱歉，不能为您提供服务，输入：{input}",
)

MULTI_PROMPT_ROUTER_TEMPLATE = """给定一个原始文本输入到\
一个语言模型并且选择最适合输入的模型提示语。＼
你将获得可用的提示语的名称以及该提示语最合适的描述。＼
如果你认为修改原始输入最终会导致语言模型得到更好的响应，你也可以修改原始输入。
<< FORMATTING >>
返回一个 Markdown 代码片段，其中 JSON 对象的格式如下：
```json
{{{{
"destination": string \ 要使用的提示语的名称或"DEFAULT"
"next_inputs": string \ 原始输入的可能修改版本
}}}}
```
记住:"destination"必须是下面指定的候选提示语中的一种，\
如果输入语句不适合任何候选提示语，则它就是"DEFAULT"。
记住:"next_inputs"可以只是原始输入，如果你认为不需要做任何修改的话。
<< CANDIDATE PROMPTS >>
{destinations}
<< INPUT >>
{{input}}
<< OUTPUT (remember to include the ```json)>>"""


router_template = MULTI_PROMPT_ROUTER_TEMPLATE.format(
    destinations=destinations_str
)

router_prompt = PromptTemplate(
    template=router_template,
    input_variables=["input"],
    output_parser=RouterOutputParser(),
)