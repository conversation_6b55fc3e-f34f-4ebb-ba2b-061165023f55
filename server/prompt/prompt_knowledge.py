from langchain.prompts import PromptTemplate
from config.prompt_config import PromptConfig

# 动态创建KNOWLEDGE_PROMPT
def get_knowledge_prompt():
    """根据当前环境返回相应的提示词对象"""
    template = PromptConfig.get_template()
    return PromptTemplate(
        input_variables=["question", "text", "graph_answer"],
        template=template,
    )

def get_environment_info():
    """获取当前环境信息"""
    return PromptConfig.get_environment_info()

# 为了保持向后兼容性，保留原有的KNOWLEDGE_PROMPT变量
KNOWLEDGE_PROMPT = get_knowledge_prompt()
