import logging
import os
from io import StringIO

from langchain_community.document_loaders import TextLoader
from langchain_community.embeddings import QianfanEmbeddingsEndpoint
from langchain_community.vectorstores import Milvus
from langchain_text_splitters import CharacterTextSplitter

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# file_path = 'D:\\data\\山能信息技术管控系统2024年8月运维服务报告.pdf'
# # 初始化pdf 文档加载器
# loader = PyPDFLoader(file_path=file_path)
# 假设这是你的长文件字符串
long_string = """这里是你的长文件内容，可以包含多行文本。
你可以在这里输入任何你需要处理的文本内容。"""

# 使用StringIO将字符串转换为文件对象
string_io = StringIO(long_string)

# 初始化文本加载器
loader = TextLoader(string_io)
# 将pdf中的文档解析为 langchain 的document对象
documents = loader.load()
# 将文档拆分为合适的大小
text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=100)
docs = text_splitter.split_documents(documents)
for doc in docs:
    print(doc.page_content)
    print('-' * 50)

# 初始化 embedding model
os.environ["QIANFAN_AK"] = "bGnvAmXrcVwLV3EbvDqYupsp"
os.environ["QIANFAN_SK"] = "UtRADjkgRgOqTaiIkmu3qj1dAStNav5C"
embeddings = QianfanEmbeddingsEndpoint()

try:
    vector = Milvus.from_documents(
        documents=documents,  # 设置保存的文档
        embedding=embeddings,  # 设置 embedding model
        collection_name="knowledge",  # 设置 集合名称
        drop_old=True,
        connection_args={
            "host": "**************",
            "port": "19530",
            "user": "root",
            "password": "YTO1545BR!4fIFmH",
            "secure": False,
            "db_name": "cmks"
        }  # Milvus连接配置
    )
    print(vector)
    logger.info("成功连接到Milvus并存储数据")
    query = "What is self-reflection of an AI Agent?"
    vector.similarity_search(query, k=1)
except Exception as e:
    logger.error(f"连接到Milvus时发生错误: {e}")
