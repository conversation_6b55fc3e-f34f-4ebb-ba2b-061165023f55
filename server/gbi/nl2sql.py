# import os
# import appbuilder
# from appbuilder.core.message import Message
# from datetime import datetime
#
# # 设置环境变量
# os.environ["APPBUILDER_TOKEN"] = "Bearer bce-v3/ALTAK-WNtGxMhzlHLfqKq8o8tlr/c54e482aaa09a73187c0efa44c08d508ee519eb6"
#
# PROMPT_TEMPLATE = """
#   MySql 表 Schema 如下:
#   {schema}
#   请根据用户当前问题，联系历史信息，仅编写1个sql，其中 sql 语句需要使用```sql ```这种 markdown 形式给出。
#   请参考列选信息：
#   {instrument}
#   请参考知识:
#   {kg}
#   当前时间：{date}
#   历史信息如下:{history_prompt}
#   当前问题："{query}"
#   回答：
# """
#
# class GBINL2Sql(object):
#
#     def __init__(self, args):
#         """
#         设置环境变量及必要数据。
#         """
#         model_name = "ERNIE-Bot 4.0"
#         self.args = args
#         table_schemas = args.table_schemas
#         self.nl2sql_node = appbuilder.NL2Sql(model_name=model_name,
#                                              table_schemas=table_schemas)
#         # self.nl2sql_node.knowledge["利润率"] = "计算方式: 利润/销售额"
#         # self.nl2sql_node.knowledge = args.knowledge
#         """
#             column_constraint = [ColumnItem(ori_value="水果",
#                                         column_value="新鲜水果",
#                                         column_name="商品类别",
#                                         table_name="超市营收明细",
#                                         is_like=False)]
#         """
#         # column_constraint = args.column_constraint
#
#     def get_sql(self, query):
#         # 获取当前时间并格式化为字符串
#         current_time = datetime.now().strftime("%Y%m%d%H%M%S")
#         msg = Message({"query": query, "date": current_time})
#         if self.args.knowledge:
#             self.nl2sql_node.knowledge = self.args.knowledge
#         result_message = self.nl2sql_node(msg)
#         return result_message.content.sql
#
