from langchain_openai import ChatOpenAI, OpenAI

from config.model_config import LLM_MODEL

config = LLM_MODEL.get("openai", {}).copy()


# openai 兼容deepseek
class OPENAI(object):
    def __init__(self, args):
        self.args = args
        # 步骤4：配置大模型
        self.model = ChatOpenAI(
            openai_api_key=config.get("api_key"),
            temperature=0.5,
            # 从DeepSeek平台获取
            # openai_api_base="https://api.deepseek.com",  # 官方API端点
            openai_api_base=config.get("api_base_url"),  # 官方API端点
            model=config.get("model_name")  # 根据场景选择模型版本
            # model="deepseek-r1"  # 根据场景选择模型版本

        )


openai = OPENAI(None)
