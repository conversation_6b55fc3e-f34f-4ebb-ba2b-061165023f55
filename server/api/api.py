from fastapi.routing import APIRouter

from .user import user_api
from .work import work_api
# from .market import market_api
from .project import project_api
from .author import author_api
from .knowledge import knowledge_api



api_router = APIRouter()

api_router.include_router(user_api, prefix="/user")
api_router.include_router(work_api, prefix="/work")
# api_router.include_router(market_api, prefix="/market")
api_router.include_router(project_api, prefix="/project")
api_router.include_router(author_api, prefix="/author")
api_router.include_router(knowledge_api, prefix="/knowledge")

__all__ = ['api_router']
