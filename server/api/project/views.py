from fastapi import APIRouter
from fastapi import Body
from pydantic import BaseModel

from server.service.acceptance_opinions import opinion

router = APIRouter()

class User(BaseModel):
    nick_name: str

class Req(BaseModel):
    text: str
    user: User


@router.post("/opinion", summary="生成验收意见")
async def create_opinion(msg: Req = Body(...)):
    print(msg)
    data = opinion(msg)
    return data

