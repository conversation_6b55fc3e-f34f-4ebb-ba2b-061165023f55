from typing import Optional

from fastapi import APIRouter
from fastapi import Body
from pydantic import BaseModel
from starlette.responses import StreamingResponse

from server.service.file_embedding import handle_embedding, handle_question, handle_suggest, handle_question_stream, \
 handleDeleteSource

router = APIRouter()


class docText(BaseModel):
    source_name: str  # 文档名称
    source_id: str  # 文档id
    full_text: str  # 全文
    tenant_id: str  # 租户id

class suggestDto(BaseModel):
    tenantIds: Optional[list] = None  # 租户id
    contentList: Optional[list] = None  # 文档名称


class questionDto(BaseModel):
    question: str  # 问题
    timestamp: str  # 时间戳
    graphAnswer: Optional[str] = None  # 图谱答案
    tenant_id: str  # 租户id



@router.post("/embedding", summary="数据向量化保存")
async def embedding(msg: docText = Body(...)):
    print(msg)
    data = handle_embedding(msg)
    return data


@router.post("/answer_stream", summary="智能问答")
async def embedding(msg: questionDto = Body(...)):
    print(msg)
    # data = handle_question(msg)
    return StreamingResponse(handle_question_stream(msg), media_type="text/event-stream")


@router.post("/answer", summary="智能问答")
async def embedding(msg: questionDto = Body(...)):
    print(msg)
    # data = handle_question(msg)
    return handle_question(msg)


@router.post("/suggest", summary="智能推荐")
async def embedding(dto: suggestDto = Body(...)):
    print(dto)
    data = handle_suggest(dto)
    # 转为json格式
    list_data = list(data)
    result = ",".join(list_data)
    return result


@router.post("/delete_embedding_by_source_id", summary="智能推荐")
async def delete_embedding_by_source_id(msg_list: list = Body(...)):
    print(msg_list)
    data = handleDeleteSource(msg_list)
    return data
