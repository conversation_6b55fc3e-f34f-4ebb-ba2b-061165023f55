from typing import Optional

from fastapi import APIRouter
from fastapi import Body
from pydantic import BaseModel
from starlette.responses import StreamingResponse

from server.service.file_embedding import handle_embedding, handle_question, handle_suggest, handle_question_stream, \
 handleDeleteSource
from server.prompt.prompt_knowledge import get_environment_info
from config.config_env import set_env_name

router = APIRouter()


class docText(BaseModel):
    source_name: str  # 文档名称
    source_id: str  # 文档id
    full_text: str  # 全文
    tenant_id: str  # 租户id

class suggestDto(BaseModel):
    tenantIds: Optional[list] = None  # 租户id
    contentList: Optional[list] = None  # 文档名称


class questionDto(BaseModel):
    question: str  # 问题
    timestamp: str  # 时间戳
    graphAnswer: Optional[str] = None  # 图谱答案
    tenant_id: str  # 租户id


class envSwitchDto(BaseModel):
    env_name: str  # 环境名称



@router.post("/embedding", summary="数据向量化保存")
async def embedding(msg: docText = Body(...)):
    print(msg)
    data = handle_embedding(msg)
    return data


@router.post("/answer_stream", summary="智能问答")
async def embedding(msg: questionDto = Body(...)):
    print(msg)
    # data = handle_question(msg)
    return StreamingResponse(handle_question_stream(msg), media_type="text/event-stream")


@router.post("/answer", summary="智能问答")
async def embedding(msg: questionDto = Body(...)):
    print(msg)
    # data = handle_question(msg)
    return handle_question(msg)


@router.post("/suggest", summary="智能推荐")
async def embedding(dto: suggestDto = Body(...)):
    print(dto)
    data = handle_suggest(dto)
    # 转为json格式
    list_data = list(data)
    result = ",".join(list_data)
    return result


@router.post("/delete_embedding_by_source_id", summary="删除向量数据")
async def delete_embedding_by_source_id(msg_list: list = Body(...)):
    print(msg_list)
    data = handleDeleteSource(msg_list)
    return data


# @router.get("/environment_info", summary="获取当前环境配置信息")
async def get_current_environment_info():
    """获取当前环境的配置信息，用于调试和验证"""
    env_info = get_environment_info()
    return {
        "success": True,
        "data": env_info,
        "message": "环境配置信息获取成功"
    }


# @router.post("/switch_environment", summary="切换环境配置（测试用）")
async def switch_environment(dto: envSwitchDto = Body(...)):
    """动态切换环境配置，用于测试不同环境的提示词"""
    try:
        # 验证环境名称是否有效
        valid_envs = ['ysprod', 'development', 'production']
        if dto.env_name not in valid_envs:
            return {
                "success": False,
                "message": f"无效的环境名称。有效的环境名称: {', '.join(valid_envs)}"
            }

        # 切换环境
        set_env_name(dto.env_name)

        # 获取新环境的信息
        new_env_info = get_environment_info()

        return {
            "success": True,
            "data": new_env_info,
            "message": f"环境已成功切换到: {dto.env_name}"
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"切换环境失败: {str(e)}"
        }
