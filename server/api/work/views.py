
from fastapi import APIRouter, Depends, Header, Request, UploadFile, HTTPException
from starlette.responses import StreamingResponse
import json

from pydantic import BaseModel
from server.service.meeting_sequential import todo
from server.service.meeting_sample import sample
from server.service.meeting_intention import intention
from server.service.meeting_router import llmrouter

router = APIRouter()

class User(BaseModel):
    nick_name: str

class Req(BaseModel):
    text: str
    user: User

@router.post("/todo", summary="生成待办")
async def get_replay(msg: Req):
    print(msg)
    data = todo(msg)

    return data

@router.post("/sample", summary="生成待办-简单示例")
async def get_replay(msg: Req):
    print(msg)
    data = sample(msg)

    return data

@router.post("/intention", summary="意图识别")
async def get_replay(msg: Req):
    print(msg)
    data = intention(msg)

    return data

@router.post("/llmrouter", summary="路由示例")
async def get_replay(msg: Req):
    print(msg)
    data = llmrouter(msg)

    return data
