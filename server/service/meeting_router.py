from langchain.chains.llm import <PERSON><PERSON>hain

from utils.utils import ArgsObject
from server import models

from langchain.prompts import PromptTemplate
from langchain.chains import <PERSON><PERSON><PERSON>er<PERSON>hain, MultiPromptChain

from server.prompt.prompt_router import default_prompt, router_prompt, destinations_str
from server.prompt.prompt_router import prompt_infos

args = ArgsObject()

# 生成待办
def llmrouter(msg):

    args.llm_name = "QIANFAN"
    # 模型初始化
    llm = getattr(models, args.llm_name)(args).llm


    # 创建目标chain
    destination_chains = {}
    for p_info in prompt_infos:
        name = p_info["name"]
        prompt_template = p_info["prompt_template"]
        prompt = PromptTemplate(
            input_variables=["input"],
            template=prompt_template,
        )
        chain = LLMChain(llm=llm, prompt=prompt)
        destination_chains[name] = chain


    
    default_chain = LLMChain(llm=llm, prompt=default_prompt)

    router_chain = LLMRouterChain.from_llm(llm, router_prompt)

    chain = MultiPromptChain(
        router_chain=router_chain,
        destination_chains=destination_chains,
        default_chain=default_chain, verbose=True
    )

    res = chain.run(msg.text)
    print(res)

    return res
