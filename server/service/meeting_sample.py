from utils.utils import ArgsObject
from server.prompt.prompt_default import  MEETING_PROMPT_FORMAT
from server.parser.parser_default import output_parsers
from datetime import datetime

from server import models

args = ArgsObject()

# 生成待办
def sample(msg):

    args.llm_name = "QIANFAN"
    # 模型初始化
    llm = getattr(models, args.llm_name)(args).llm

    # 研发二部负责明天开展重点工作产品开发，完成产品发版上线
    final_prompt = MEETING_PROMPT_FORMAT.format(context=msg.text, current_time=datetime.now().strftime("%Y%m%d%H%M%S"))

    return output_parsers.parse(llm(final_prompt))
