import time

from langchain_community.embeddings import QianfanEmbeddingsEndpoint

from server.embedding.milvus_client import cmks_milvus_client
from server.models.openai import openai
from server.models.qianfan import qianfan
from server.prompt.prompt_knowledge import KNOWLEDGE_PROMPT


def generate_prompt(question, context, graph_answer):
    """生成提示词"""
    opinion_prompt = KNOWLEDGE_PROMPT.format(question=question, text=context, graph_answer=graph_answer)
    print("提示词为:" + opinion_prompt)
    return opinion_prompt


# 定义拆分函数
def split_text_with_overlap(text, chunk_size=500, overlap=100):
    chunks = []
    start = 0
    while start < len(text):
        end = min(start + chunk_size, len(text))
        chunks.append(text[start:end])
        start += chunk_size - overlap
    return chunks


def handle_embedding(doc_text):
    # 加载千帆向量模型
    embeddings = QianfanEmbeddingsEndpoint()
    # 定义大文本
    large_text = doc_text.full_text

    # 拆分文本
    texts = split_text_with_overlap(large_text, chunk_size=300, overlap=100)
    # 准备数据
    data = []
    for i, textStr in enumerate(texts):
        if not textStr.strip():
            continue
        text_embedding = embeddings.embed_query(textStr)
        source_name = doc_text.source_name
        source_id = doc_text.source_id
        text = textStr
        data.append({
            # "pk": f"{source_id}_{i}",  # 添加主键字段，使用source_id和索引组合确保唯一性
            "vector": text_embedding,
            "source_name": source_name,
            "source_id": source_id,
            "text": text,
            "page": i,
            "tenant_id": doc_text.tenant_id
        })
    # 插入数据到Milvus
    res = cmks_milvus_client.insert(
        collection_name="cmks_file",
        data=data
    )

    print(res)
    return True


def retrieve_context(question, tenant_id):
    # 假设 QianfanEmbeddingsEndpoint
    embeddings = QianfanEmbeddingsEndpoint()
    # 需要向量化的文本
    # 将文本向量化
    query_vector = embeddings.embed_query(question)
    # 指定要返回的所有字段
    output_fields = ['source_id', "source_name", "text"]  # 替换为实际的字段名
    # 将逗号分隔的 tenant_id 转换为列表，并格式化
    tenant_id_list = tenant_id.split(",")
    formatted_tenant_ids = [f'"{tid.strip()}"' for tid in tenant_id_list]
    filter_condition = f'tenant_id in [{", ".join(formatted_tenant_ids)}]'
    search_params = {
        "metric_type": "COSINE",
        "params": {
            "radius": 0.5      # 最低相似度阈值（仅保留 score >= 0.6）[-1,1]
            # "range_filter": 1.0   # 可选：进一步过滤更高相似度的结果
        }
    }


    res = cmks_milvus_client.search(
        collection_name="cmks_file",
        anns_field="vector",
        data=[query_vector],
        limit=1,
        # filter=f'code == "{code_value}"',
        filter=filter_condition,
        search_params=search_params,
        output_fields=output_fields
    )
    source_text = list()
    source_id_list = list()
    for hits in res:
        for hit in hits:
            entity = hit.get("entity")
            print(entity)
            source_name = entity['source_name']
            source_id = entity['source_id']
            text = entity['text']
            print("检索到的数据为:")
            print(source_name, source_id, text)
            source_text.append(text)
            source_id_list.append(source_id)
    return "\n".join(source_text), "\n".join(source_id_list)


def get_answer_from_llm(prompt):
    """从大模型获取答案"""
    response = qianfan.llm(prompt=prompt)
    return response.strip()


def get_answer_from_llm_stream(prompt):
    response = ""
    """从大模型获取答案"""
    # response = qianfan.llm.stream(prompt=prompt)
    # 这段代码的耗时
    start_time = time.time()
    for chunk in openai.model.stream(prompt):
        print(chunk.content)
        end_time = time.time()
        print("大模型回复的耗时:", end_time - start_time)
        response += chunk.content
        yield chunk.content
        # return response.strip()


def handle_question(questionDto):
    question = questionDto.question
    graph_answer = questionDto.graphAnswer
    # 获取相似的文本片段
    context, source_id = retrieve_context(question, questionDto.tenant_id)
    # 构建提示词
    prompt = generate_prompt(question, context, graph_answer)
    # llm
    answer = get_answer_from_llm(prompt)
    return answer


def handle_question_stream(questionDto):
    question = questionDto.question
    graph_answer = questionDto.graphAnswer
    # 获取相似的文本片段
    context, source_id = retrieve_context(question, questionDto.tenant_id)
    # 构建提示词
    prompt = generate_prompt(question, context, graph_answer)
    # llm
    for chunk in get_answer_from_llm_stream(prompt):
        yield chunk
    yield "\n"
    yield "$end:" + source_id


# 智能推荐
def handle_suggest(dto):
    msg_list = dto.contentList
    tenant_id_list = dto.tenantIds
    # 假设 QianfanEmbeddingsEndpoint
    embeddings = QianfanEmbeddingsEndpoint()
    # 需要向量化的文本
    # 将文本向量化
    query_vector_list = []
    time1 = time.time()
    # msg_list过滤掉空值
    msg_list = [msg for msg in msg_list if msg]
    query_vector_list = (embeddings.embed_documents(msg_list))
    time2 = time.time()
    print("向量化的耗时:", time2 - time1)
    # 指定要返回的所有字段
    formatted_tenant_ids = [f'"{tid.strip()}"' for tid in tenant_id_list]
    filter_condition = f'tenant_id in [{", ".join(formatted_tenant_ids)}]'
    output_fields = ['source_id', "source_name", "text"]  # 替换为实际的字段名
    res = cmks_milvus_client.search(
        collection_name="cmks_file",
        anns_field="vector",
        data=query_vector_list,
        limit=50,
        filter=filter_condition,
        output_fields=output_fields
    )
    source_file_list = set()
    for hits in res:
        for hit in hits:
            entity = hit.get("entity")
            print(entity)
            source_name = entity['source_name']
            source_id = entity['source_id']
            text = entity['text']
            print("检索到的数据为:")
            print(source_name, source_id, text)
            source_file_list.add(source_id)
            if len(source_file_list) >= 7:
                return source_file_list
    return source_file_list


def handleDeleteSource(source_id_list):
    """
    根据 source_id 删除向量数据库中的数据
    """
    # sourcr_id_list改为 filter="color in ['red_3314', 'purple_7392']" 这个格式的字段串
    source_id_list_filter = "source_id in " + str(source_id_list)
    # 定义要删除的条件
    # 删除数据
    res = cmks_milvus_client.delete(
        collection_name="cmks_file",
        filter=source_id_list_filter
    )
    #将res转为str
    res_str = str(res)
    print("milvus 删除数据结果为:"+res_str )
    return True
