from utils.utils import ArgsObject
from langchain.chains import <PERSON><PERSON>hai<PERSON>
from langchain.chains import SimpleSequential<PERSON>hain

from server.prompt.prompt_meeting import  MEETING_PROMPT, MEETING_PROMPT_FORMAT
from server.parser.parser_default import output_parsers
from server import models


args = ArgsObject()

# 生成待办
def todo(msg):
    args.llm_name = "QIANFAN"
    # 模型初始化
    llm = getattr(models, args.llm_name)(args).llm

    meeting_chain= LLMChain(llm=llm, prompt=MEETING_PROMPT)

    format_chain= LLMChain(llm=llm, prompt=MEETING_PROMPT_FORMAT)

    # 顺序链
    overall_chain = SimpleSequentialChain(
        chains=[meeting_chain, format_chain],
        verbose=True
    )

    # 会议同意由南矿能源收购电力集团所持南矿煤化工程有限公司100%股权，以2022年9月30日为基准日，按照经电力集团备案的评估价格2090.4万元转让。此事项由电力集团资本运营部负责牵头抓好落实，于11月底前完成收购。
    review = overall_chain.run(msg.text)

    return output_parsers.parse(review)
