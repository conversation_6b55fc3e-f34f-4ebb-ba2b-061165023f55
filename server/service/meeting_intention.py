from utils.utils import ArgsObject
from server.prompt.prompt_action import PROMPT
from server import models
from server.parser.parser_action import output_parsers


args = ArgsObject()
def intention(msg):

    args.llm_name = "QIANFAN"
    # 模型初始化
    llm = getattr(models, args.llm_name)(args).llm

    # 帮我生成待办，会议同意由南矿能源收购电力集团所持南矿煤化工程有限公司100%股权，以2022年9月30日为基准日，按照经电力集团备案的评估价格2090.4万元转让。此事项由电力集团资本运营部负责牵头抓好落实，于11月底前完成收购。
    final_prompt = PROMPT.format(context=msg.text)

    return output_parsers.parse(llm(final_prompt))
