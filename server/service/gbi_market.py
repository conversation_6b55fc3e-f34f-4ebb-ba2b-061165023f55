# from utils.utils import ArgsObject
# from server.gbi.nl2sql import GBINL2Sql
# from server.gbi.select_table import GBISelectTable
# from server.db.database import db
#
# from server.prompt.prompt_market import MARKET_PROMPT
# from server import models
#
# args = ArgsObject()
#
# table_descriptions = {
#     "supper_market_info": "超市营收明细表，包含超市各种信息等",
#     "product_sales_info": "产品收入表"
# }
#
# SUPER_MARKET_SCHEMA = """
# ```
# CREATE TABLE `supper_market_info` (
#   `order_number` varchar(32) DEFAULT NULL COMMENT '订单号',
#   `order_date` date DEFAULT NULL COMMENT '订单日期',
#   `mailing` varchar(32) DEFAULT NULL COMMENT '邮寄方式',
#   `area` varchar(32) DEFAULT NULL COMMENT '地区',
#   `province` varchar(32) DEFAULT NULL COMMENT '省份',
#   `custom_type` varchar(32) DEFAULT NULL COMMENT '客户类型',
#   `custom_name` varchar(32) DEFAULT NULL COMMENT '客户名称',
#   `custom_category` varchar(32) DEFAULT NULL COMMENT '商品类别',
#   `producer` varchar(32) DEFAULT NULL COMMENT '制造商',
#   `trade_name` varchar(32) DEFAULT NULL COMMENT '商品名称',
#   `number` int(11) DEFAULT NULL COMMENT '数量',
#   `sales` int(11) DEFAULT NULL COMMENT '销售额',
#   `profit` int(11) DEFAULT NULL COMMENT '利润'
# ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
# ```
# """
#
# PRODUCT_SALES_INFO = """
# 现有 mysql 表 product_sales_info,
# 该表的用途是: 产品收入表
# ```
# CREATE TABLE `product_sales_info` (
#   `year` int COMMENT '年',
#   `month` int COMMENT '月',
#   `trade_name` varchar COMMENT '产品名称',
#   `income` decimal COMMENT '收入',
#   `pay` decimal COMMENT '非交付成本',
#   `gross` decimal COMMENT '含交付毛利'
# )
# ```
# """
#
# table_name_compare = {
#     "supper_market_info": SUPER_MARKET_SCHEMA,
#     "product_sales_info": PRODUCT_SALES_INFO
# }
#
# # 生成待办
# def market(msg):
#
#     args.table_descriptions = table_descriptions
#     # 1、先选表
#     table = GBISelectTable(args).get_table(msg.query)
#
#     print("============表：" + ', '.join(table))
#
#     # 2、根据表选择schma
#     table = ["supper_market_info", "table2"]
#     table_name_compare = {
#         "supper_market_info": SUPER_MARKET_SCHEMA,
#         "product_sales_info": PRODUCT_SALES_INFO
#     }
#     table_schmas = []
#     # 遍历table列表
#     for name in table:
#         # 检查name是否在table_name_compare字典的键中
#         if name in table_name_compare:
#             # 如果存在，则将对应的值添加到结果列表中
#             table_schmas.append(table_name_compare[name])
#
#     # 3、获取sql
#     args.table_schemas = table_schmas
#     args.knowledge = msg.knowledge
#     nl2sql = GBINL2Sql(args).get_sql(msg.query)
#     print("============SQL：" + nl2sql)
#
#     # 4、执行查询语句
#     result = db.execute_query(nl2sql)
#     # 将结果集转换为人类可读格式的字符串
#     output_lines = []
#     for row in result:
#         output_line = ' '.join(str(item) for item in row)
#         output_lines.append(output_line)
#
#     result_str = '\n'.join(output_lines)
#     print("============result：" + str(result_str))
#
#
#     # 5、大模型润色
#     args.llm_name = "QIANFAN"
#     # 模型初始化
#     llm = getattr(models, args.llm_name)(args).llm
#     final_prompt = MARKET_PROMPT.format(query=msg.query, result=result_str)
#
#     return llm(final_prompt)
