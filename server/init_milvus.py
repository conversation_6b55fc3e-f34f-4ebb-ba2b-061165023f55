import os
from pymilvus import MilvusClient, DataType, utility, connections

from config.config_env import env_name
from config.config_milvus import load_milvus_config


def init_milvus_collections():
    """
    初始化Milvus集合
    创建必要的集合和索引
    """
    print("正在初始化Milvus集合...")

    # 加载Milvus配置
    milvus_uri, milvus_user, milvus_password, milvus_db_name = load_milvus_config(env_name)

    print(f"正在连接到Milvus服务器: {milvus_uri}, 用户: {milvus_user}, 数据库: {milvus_db_name}")

    # 先创建连接
    try:
        connections.connect(
            alias="default",
            uri=milvus_uri,
            user=milvus_user,
            password=milvus_password,
            db_name=milvus_db_name
        )
        print(f"成功连接到Milvus服务器: {milvus_uri}")
    except Exception as e:
        print(f"连接Milvus服务器失败: {e}")
        raise e

    # 创建Milvus客户端
    try:
        client = MilvusClient(
            uri=milvus_uri,
            user=milvus_user,
            password=milvus_password,
            db_name=milvus_db_name
        )
        print("成功创建Milvus客户端")
    except Exception as e:
        print(f"创建Milvus客户端失败: {e}")
        raise e

    # 检查cmks_file集合是否存在
    collection_name = "cmks_file"
    try:
        # 使用client.has_collection方法检查集合是否存在
        if client.has_collection(collection_name):
            print(f"集合{collection_name}已存在，跳过创建步骤...")
            return  # 如果集合已存在，直接返回，不进行后续创建操作
    except Exception as e:
        print(f"检查集合是否存在时出错: {e}")
        # 继续执行，尝试创建集合

    print(f"开始创建集合{collection_name}...")
    # 创建集合schema
    schema = MilvusClient.create_schema()

    # 添加主键字段
    schema.add_field(
        field_name="pk",
        datatype=DataType.VARCHAR,
        is_primary=True,
        max_length=64,
        auto_id=True
    )
    # 添加向量字段
    schema.add_field(
        field_name="vector",
        datatype=DataType.FLOAT_VECTOR,
        dim=384  # 根据您的向量维度调整
    )

    # 添加其他字段
    schema.add_field(
        field_name="source_name",
        datatype=DataType.VARCHAR,
        max_length=1000
    )

    schema.add_field(
        field_name="source_id",
        datatype=DataType.VARCHAR,
        max_length=50
    )

    schema.add_field(
        field_name="text",
        datatype=DataType.VARCHAR,
        max_length=65535
    )

    schema.add_field(
        field_name="page",
        datatype=DataType.INT64
    )

    schema.add_field(
        field_name="tenant_id",
        datatype=DataType.VARCHAR,
        max_length=6
    )

    # 创建集合
    print(f"正在创建{collection_name}集合...")
    client.create_collection(
        collection_name=collection_name,
        schema=schema,
        consistency_level="Strong"
    )

    # 创建向量索引
    print("正在创建向量索引...")

    # 使用prepare_index_params创建索引参数对象
    index_params = client.prepare_index_params()

    # 添加向量字段的索引
    index_params.add_index(
        field_name="vector",
        index_type="HNSW",
        metric_type="COSINE",
        params={"M": 8, "efConstruction": 200}
    )

    # 创建索引
    try:
        client.create_index(
            collection_name=collection_name,
            index_params=index_params
        )
        print(f"成功为集合{collection_name}创建索引")
    except Exception as e:
        print(f"创建索引失败: {e}")
        raise e

    print("Milvus集合初始化完成！")


if __name__ == "__main__":
    init_milvus_collections()
