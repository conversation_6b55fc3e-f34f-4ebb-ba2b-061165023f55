#!/bin/bash

# 初始化Milvus集合的Shell脚本
# 用法: ./init_milvus.sh [--drop]
# 参数:
#   --drop: 删除现有集合并重新创建

# 设置Python环境
# 如果使用虚拟环境，取消下面的注释并修改路径
# source venv/bin/activate

# 检查是否有--drop参数
if [ "$1" == "--drop" ]; then
    echo "将删除现有集合并重新创建..."
    python server/init_milvus.py --drop
else
    echo "保留现有集合，仅创建不存在的集合..."
    python server/init_milvus.py
fi

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "Milvus集合初始化成功!"
else
    echo "Milvus集合初始化失败，请检查错误信息。"
    exit 1
fi
