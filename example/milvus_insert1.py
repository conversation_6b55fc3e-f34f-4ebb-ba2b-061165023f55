
import os
from pymilvus import MilvusClient
from langchain_community.embeddings import QianfanEmbeddingsEndpoint

os.environ["QIANFAN_AK"] = "7Ipu16Ub12oICOLXLG9VLZDk"
os.environ["QIANFAN_SK"] = "Qo8YVuvgjyheMFpsehlBA5EFghiXY16E"

client = MilvusClient(
    uri="http://**************:19530",
    user="root",
    password="YTO1545BR!4fIFmH",
    db_name="cmks"
)

# 加载千帆向量模型
embeddings = QianfanEmbeddingsEndpoint()

# 定义大文本
large_text = """
可以在这个页面中查看和执行年度重点工作，可以根据这些条件筛选：关联关系(我下发的、我负责的)、状态(进行中、正常推进、已逾期、待审核、已完成、已撤销)、责任人(人物名称或称谓,例如：张三或张总)、工作内容(某项工作的简要说明，例如：拓展销售渠道)、所属业务(云小督、设备)、
所属部门(部门名称)、年度(具体年份或泛称，例如2024、今年)、完成时限(时间区域或泛称，例如2024年12月9日到2024年12月15日、上周、昨天)，页面中的操作：督办（keyTask:myWork:supervise）、撤销（keyTask:myWork:withdraw）、完结（keyTask:myWork:finish）、
修改（keyTask:myWork:edit）、关联工作（keyTask:myWork:associate）、反馈进度（keyTask:myWork:report）、分解工作（keyTask:myWork:break）

可以在这个页面中查看和管理所有的年度重点工作，可以根据这些条件筛选：年度(具体年份或泛称，例如2024、今年)、下发人((人物名称或称谓,例如：张三或张总))、责任人((人物名称或称谓,例如：张三或张总))、工作内容(某项工作的简要说明，例如：拓展销售渠道)、所属业务(云小督、设备)、
所属部门(部门名称)、完成时限(时间区域或泛称，例如2024年12月9日到2024年12月15日、上周、昨天)，页面中的操作：
创建（keyTask:manage:create）、编辑（keyTask:manage:edit）、分解工作（keyTask:manage:break）、督办（keyTask:manage:supervise）、
撤销（keyTask:manage:withdraw）

可以在这个页面中可以查看和管理所有的日常工作，可以根据这些条件筛选：关联关系(我下发的、我负责的、我代发的)、状态(全部、进行中、正常推进、已逾期、待审核、已完成、已撤销)、工作内容(某项工作的简要说明，例如：拓展销售渠道)、
下发时间(时间区域或泛称，例如2024年12月9日到2024年12月15日、上周、昨天)、责任人(人物名称或称谓,例如：张三或张总)、完成时限(时间区域或泛称，例如2024年12月9日到2024年12月15日、上周、昨天)，页面中的操作：下发日常工作（daily:assign）、修改（daily:edit）、撤销（daily:withdraw）、督办（daily:supervise）、
反馈进度（daily:report）、完结（daily:finish）、分解工作（daily:break）
"""

# 定义拆分函数
def split_text_with_overlap(text, chunk_size=500, overlap=100):
    chunks = []
    start = 0
    while start < len(text):
        end = min(start + chunk_size, len(text))
        chunks.append(text[start:end])
        start += chunk_size - overlap
    return chunks

# 拆分文本
texts = split_text_with_overlap(large_text, chunk_size=500, overlap=100)

# 准备数据
data = []
for i, textStr in enumerate(texts):
    if not textStr.strip():
        continue
    text_embedding = embeddings.embed_query(textStr)
    source_name="数据统计文档"
    source_id = "123123123123123"
    text=textStr
    data.append({
        "vector": text_embedding,
        "source_name": source_name,
        "source_id": source_id,
        "text": text,
        "page": i
    })

# 插入数据到Milvus
res = client.insert(
    collection_name="cmks_file",
    data=data
)
print(res)
