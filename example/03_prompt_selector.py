import os
from langchain_community.llms import QianfanLLMEndpoint
from langchain.prompts import FewShotPromptTemplate, PromptTemplate
from langchain.prompts.example_selector import SemanticSimilarityExampleSelector
from langchain.vectorstores import FAISS
from langchain.embeddings import OpenAIEmbeddings
from langchain.embeddings.baidu_qianfan_endpoint import QianfanEmbeddingsEndpoint


# qianfan sdk 鉴权
os.environ["QIANFAN_AK"] = "7Ipu16Ub12oICOLXLG9VLZDk"
os.environ["QIANFAN_SK"] = "Qo8YVuvgjyheMFpsehlBA5EFghiXY16E"
api_key="***************************************************"

llm = QianfanLLMEndpoint(model="ERNIE-Bot-turbo", temperature=0.5)


template = """
示例输入：
{input_text}
示例输出：
{output_text}
"""

examples = [
    {"input_text": "海盗", "output_text": "船"},
    {"input_text": "飞行员", "output_text": "飞机"},
    {"input_text": "驾驶员", "output_text": "汽车"},
    {"input_text": "树", "output_text": "地面"},
    {"input_text": "鸟", "output_text": "鸟巢"},
] 

"""
Args：
     examples:要在提示中使用的示例列表。
     嵌入式：一个初始化的嵌入API接口，例如OpenAIEmbeddings（）。
     vectorstore_cls：矢量存储数据库接口类，例如FAISS。
     k： 要选择的示例数
     input_keys：如果提供，则搜索基于输入变量而不是所有变量。
     vectorstore_cls_kwargs：包含矢量存储url的可选kwargs
"""
example_selector = SemanticSimilarityExampleSelector.from_examples(
    examples,
    QianfanEmbeddingsEndpoint(),
    FAISS,
    k=2
)
example_prompt = PromptTemplate(input_variables=["input_text", "output_text"], template=template)


similar_prompt = FewShotPromptTemplate(
     # 示例对象
     example_selector=example_selector,
     # 提示词
     example_prompt=example_prompt,
     # 提示词顶部和底部的自定义项
     prefix="根据下面示例写出",
     suffix="输入：{noun} \n输出：",
     # 提示词接收的输入
     input_variables=["noun"]
)

my_noun = "学生"

print(similar_prompt.format(noun=my_noun))
