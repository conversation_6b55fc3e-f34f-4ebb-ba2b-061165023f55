from langchain import hub
from langchain.chains import RetrievalQA
import os

from langchain.indexes import vectorstore
from langchain_community.llms import QianfanLLMEndpoint
# qianfan sdk 鉴权
os.environ["QIANFAN_AK"] = "7Ipu16Ub12oICOLXLG9VLZDk"
os.environ["QIANFAN_SK"] = "Qo8YVuvgjyheMFpsehlBA5EFghiXY16E"

llm = QianfanLLMEndpoint(model="ERNIE-Bot-turbo", temperature=0.5)

query = input("\nQuery: ")
prompt = hub.pull("rlm/rag-prompt")
qa_chain = RetrievalQA.from_chain_type( llm, retriever=vectorstore.as_retriever(), chain_type_kwargs={"prompt": prompt})
result = qa_chain({"query": query})
print(result)
