import os

from langchain_community.chat_models import QianfanChatEndpoint
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder

from server.prompt.prompt_menu_confirm import template_menu_format

os.environ["QIANFAN_AK"] = "7Ipu16Ub12oICOLXLG9VLZDk"
os.environ["QIANFAN_SK"] = "Qo8YVuvgjyheMFpsehlBA5EFghiXY16E"

# qianfan_chat = QianfanLLMEndpoint(
#     model="ERNIE-Bot", temperature=0.1
# )
# 创建千帆LLM模型
qianfan_chat = QianfanChatEndpoint(
    model="ERNIE-Bot",
    temperature=0.1,
    timeout=30,
)
prompt = ChatPromptTemplate.from_messages(
    [
        ("system", template_menu_format),
        MessagesPlaceholder("chat_history"),
        ("human", "{input}"),
    ]
)

chain = prompt | qianfan_chat

chat_history = []

question = "我要下发工作？"
ai_msg_1 = chain.invoke({"input": question, "chat_history": chat_history})
print("1、" + ai_msg_1.content)
chat_history.extend([HumanMessage(content=question), AIMessage(content=ai_msg_1.content)])

second_question = "我要下发一个关于公司统一框架升级的工作？"
ai_msg_2 = chain.invoke({"input": second_question, "chat_history": chat_history})
print("2、" + ai_msg_2.content)
chat_history.extend([HumanMessage(content=second_question), AIMessage(content=ai_msg_2.content)])

third_question = "可以"
ai_msg_3 = chain.invoke({"input": third_question, "chat_history": chat_history})
print(ai_msg_3.content)
chat_history.extend([HumanMessage(content=third_question), AIMessage(content=ai_msg_3.content)])
print(chat_history)

