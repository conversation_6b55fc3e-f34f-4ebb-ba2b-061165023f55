import os

from langchain_community.embeddings import QianfanEmbeddingsEndpoint
from langchain_community.llms.baidu_qianfan_endpoint import QianfanLLMEndpoint
from pymilvus import MilvusClient

from server.parser.parser_menu import menu_output_parsers
from server.prompt.prompt_menu import MENU_PROMPT_FORMAT

os.environ["QIANFAN_AK"] = "7Ipu16Ub12oICOLXLG9VLZDk"
os.environ["QIANFAN_SK"] = "Qo8YVuvgjyheMFpsehlBA5EFghiXY16E"

llm = QianfanLLMEndpoint(model="ERNIE-Bot", temperature=0.1)
client = MilvusClient(
    uri="http://**************:19530",
    user="root",
    password="2Ghlmcl@szyk",
    db_name="keywork"

)
# 加载千帆向量模型
# 假设 QianfanEmbeddingsEndpoint 类有一个 embed_query 方法
embeddings = QianfanEmbeddingsEndpoint()

# 需要向量化的文本
# question = ("我要查看张总上个月下发给我的有关设备的年度重点工作")
question = ("下发年度重点工作")

# 将文本向量化
query_vector = embeddings.embed_query(question)

# 指定要返回的所有字段
output_fields = ['code', "area_code", "description"]  # 替换为实际的字段名
code_value = 'keywork'
res = client.search(
    collection_name="menu",
    anns_field="desc_vector",
    data=[query_vector],
    limit=1,
    filter=f'code == "{code_value}"',
    output_fields=output_fields
)
for hits in res:
    for hit in hits:
        entity = hit.get("entity")
        print(entity)
        code = entity['code']
        area_code = entity['area_code']
        function_introduction = entity['description']

final_prompt = MENU_PROMPT_FORMAT.format(question=question, context=function_introduction)
print(llm(final_prompt))

answer = menu_output_parsers.parse(llm(final_prompt))
answer['code'] = code
answer['area_code'] = area_code
print(answer)
