import os
from langchain.output_parsers import StructuredOutputParser, ResponseSchema
from langchain.prompts import PromptTemplate
from langchain_community.llms import QianfanLLMEndpoint

# qianfan sdk 鉴权
os.environ["QIANFAN_AK"] = "7Ipu16Ub12oICOLXLG9VLZDk"
os.environ["QIANFAN_SK"] = "Qo8YVuvgjyheMFpsehlBA5EFghiXY16E"

llm = QianfanLLMEndpoint(model="ERNIE-Bot-turbo", temperature=0.5)

# 大模型回复结构
response_schema = [
    ResponseSchema(name="bad_string", description="这是一个不正确的用户输入的格式"),
    ResponseSchema(name="good_string", description="这是您的回复，重新格式化的回复")
]

output_parsers = StructuredOutputParser.from_response_schemas(response_schema)

format_instructions = output_parsers.get_format_instructions()
print(format_instructions)

template = """
您将从用户那里得到一个不正确的字符串，重新格式化并确保所以的拼写检查正确
{format_instructions}
用户输入：
{user_input}
你的响应：
"""

prompt = PromptTemplate(
    input_variables=["user_input"],
    partial_variables={"format_instructions": format_instructions},
    template=template
)

final_prompt = prompt.format(user_input="欢迎广州来到")

print(llm(final_prompt))
