import os
from langchain.agents import load_tools
from langchain.agents import initialize_agent
from langchain.text_splitter import RecursiveCharacterTextSplitter

from langchain_community.llms import QianfanLLMEndpoint

# qianfan sdk 鉴权
os.environ["QIANFAN_AK"] = "7Ipu16Ub12oICOLXLG9VLZDk"
os.environ["QIANFAN_SK"] = "Qo8YVuvgjyheMFpsehlBA5EFghiXY16E"

llm = QianfanLLMEndpoint(model="ERNIE-Bot-turbo", temperature=0.5)

"""
Args：
    tool_names：要加载的工具的名称。
    llm：一个可选的语言模型，可能需要初始化某些工具。
    callbacks：可选的回调管理器或回调处理程序列表。如果没有提供，将使用默认的全局回调管理器。
"""
# 加载工具，谷歌搜索，需要用到api_key
tookit = load_tools(['serpapi'], llm=llm, serpapi_api_key="")
"""
Args：
    tools：此代理有权访问的工具列表。
    llm：用作代理的语言模型。
    agent：要使用的代理类型。如果“无”且agent_path也为“无”，则默认为
    AgentType。ZERO_SHOT_REACT_DESCRIPTION。
    callback_manager:要使用的CallbackManager。如果未提供。默认为“无”。
    agent_path：要使用的序列化代理的路径。
    agent_kwargs：要传递给基础代理的其他关键字参数
    标记：应用于跟踪管路的标记。
    **kwargs：传递给代理执行器的其他关键字参数
"""
agent = initialize_agent(tookit, llm, agent="zero-shot-react-description", verbose=True, return_intermediate=True)

response = agent.run("input", "蔡徐坤的第一章专辑是什么")

print(response)
