import os
from langchain.chains import <PERSON><PERSON><PERSON><PERSON>
from langchain.chains import SimpleSe<PERSON><PERSON>hai<PERSON>
from langchain.prompts import PromptTemplate
from langchain_community.llms import QianfanLLMEndpoint

# qianfan sdk 鉴权
os.environ["QIANFAN_AK"] = "7Ipu16Ub12oICOLXLG9VLZDk"
os.environ["QIANFAN_SK"] = "Qo8YVuvgjyheMFpsehlBA5EFghiXY16E"

llm = QianfanLLMEndpoint(model="ERNIE-Bot-turbo", temperature=0.5)

template = """
您的工作是根据用户输入的区域提出一道经典的菜肴
用户输入：
{user_input}
你的响应：
"""

prompt_template = PromptTemplate(
    input_variables=["user_input"],
    template=template
)

location_chain= LLMChain(llm=llm, prompt=prompt_template)

template = """
给出一道简单的食谱，说明如何在家做这道菜
菜谱：
{user_meal}
你的响应：
"""

prompt_template = PromptTemplate(
    input_variables=["user_meal"],
    template=template
)

meal_chain = LLMChain(llm=llm, prompt=prompt_template)

overall_chain = SimpleSequentialChain(
    chains=[location_chain, meal_chain],
    verbose=True
)

review = overall_chain.run("山东济南")

print(review)
