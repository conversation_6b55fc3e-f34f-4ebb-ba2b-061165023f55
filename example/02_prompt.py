import os
from langchain_community.llms import QianfanLLMEndpoint
from langchain.prompts import PromptTemplate

# qianfan sdk 鉴权
os.environ["QIANFAN_AK"] = "7Ipu16Ub12oICOLXLG9VLZDk"
os.environ["QIANFAN_SK"] = "Qo8YVuvgjyheMFpsehlBA5EFghiXY16E"

llm = QianfanLLMEndpoint(model="ERNIE-Bot-turbo", temperature=0.5)

prompt = """
1+1=2，是不是错误的。
"""

print(llm(prompt))

template = """
我想问你一个问题，
{question}
"""

prompt = PromptTemplate(template=template, input_variables=["question"])

final_prompt = prompt.format(question="你好吗？")

print(llm(final_prompt))
