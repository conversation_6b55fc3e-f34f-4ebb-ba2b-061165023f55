import os
from pymilvus import MilvusClient
from langchain_community.embeddings import QianfanEmbeddingsEndpoint

os.environ["QIANFAN_AK"] = "7Ipu16Ub12oICOLXLG9VLZDk"
os.environ["QIANFAN_SK"] = "Qo8YVuvgjyheMFpsehlBA5EFghiXY16E"

client = MilvusClient(
    uri="http://**************:19530",
    user="root",
    password="2Ghlmcl@szyk",
    db_name="keywork"
    # uri="http://**************:19530",
    # user="root",
    # password="YTO1545BR!4fIFmH",
    # db_name="keywork"

)

# 加载千帆向量模型
# 假设 QianfanEmbeddingsEndpoint 类有一个 embed_query 方法
embeddings = QianfanEmbeddingsEndpoint()

# 需要向量化的文本
text1 = ("可以在这个页面中查看和执行年度重点工作，可以根据这些条件筛选：关联关系(我下发的、我负责的)、状态(进行中、正常推进、已逾期、待审核、已完成、已撤销)、责任人(人物名称或称谓,例如：张三或张总)、工作内容(某项工作的简要说明，例如：拓展销售渠道)、所属业务(云小督、设备)、"
         "所属部门(部门名称)、年度(具体年份或泛称，例如2024、今年)、完成时限(时间区域或泛称，例如2024年12月9日到2024年12月15日、上周、昨天)，页面中的操作：督办（keyTask:myWork:supervise）、撤销（keyTask:myWork:withdraw）、完结（keyTask:myWork:finish）、"
         "修改（keyTask:myWork:edit）、关联工作（keyTask:myWork:associate）、反馈进度（keyTask:myWork:report）、分解工作（keyTask:myWork:break）")
text2 = ("可以在这个页面中查看和管理所有的年度重点工作，可以根据这些条件筛选：年度(具体年份或泛称，例如2024、今年)、下发人((人物名称或称谓,例如：张三或张总))、责任人((人物名称或称谓,例如：张三或张总))、工作内容(某项工作的简要说明，例如：拓展销售渠道)、所属业务(云小督、设备)、"
         "所属部门(部门名称)、完成时限(时间区域或泛称，例如2024年12月9日到2024年12月15日、上周、昨天)，页面中的操作："
         "创建（keyTask:manage:create）、编辑（keyTask:manage:edit）、分解工作（keyTask:manage:break）、督办（keyTask:manage:supervise）、"
         "撤销（keyTask:manage:withdraw）")
text3 = ("可以在这个页面中可以查看和管理所有的日常工作，可以根据这些条件筛选：关联关系(我下发的、我负责的、我代发的)、状态(全部、进行中、正常推进、已逾期、待审核、已完成、已撤销)、工作内容(某项工作的简要说明，例如：拓展销售渠道)、"
         "下发时间(时间区域或泛称，例如2024年12月9日到2024年12月15日、上周、昨天)、责任人(人物名称或称谓,例如：张三或张总)、完成时限(时间区域或泛称，例如2024年12月9日到2024年12月15日、上周、昨天)，页面中的操作：下发日常工作（daily:assign）、修改（daily:edit）、撤销（daily:withdraw）、督办（daily:supervise）、"
         "反馈进度（daily:report）、完结（daily:finish）、分解工作（daily:break）")

# 将文本向量化
text_embedding1 = embeddings.embed_query(text1)
text_embedding2 = embeddings.embed_query(text2)
text_embedding3 = embeddings.embed_query(text3)
# 将实体插入 Collections
data = [
    {"desc_vector": text_embedding1, "name": "年度重点工作", "code": "keywork", "area_code": "keyTask:myWork",
     "description": text1},
{"desc_vector": text_embedding2, "name": "年度重点工作", "code": "keywork", "area_code": "keyTask:manage",
     "description": text2},
{"desc_vector": text_embedding3, "name": "日常工作", "code": "dailyWork", "area_code": "",
     "description": text3}
]
res = client.insert(
    collection_name="menu",
    data=data
)


