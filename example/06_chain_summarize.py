import os
from langchain.chains.summarize import load_summarize_chain
from langchain.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter

from langchain_community.llms import QianfanLLMEndpoint

# qianfan sdk 鉴权
os.environ["QIANFAN_AK"] = "7Ipu16Ub12oICOLXLG9VLZDk"
os.environ["QIANFAN_SK"] = "Qo8YVuvgjyheMFpsehlBA5EFghiXY16E"

llm = QianfanLLMEndpoint(model="ERNIE-Bot-turbo", temperature=0.5)


loader = TextLoader('data/data.txt', 'utf-8')
documents = loader.load()

text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=100)

texts = text_splitter.split_documents(documents)

"""
Args：
    llm：在链中使用的语言模型。
    chain_type：要使用的文档组合链的类型。应该是“stuff”、“map_reduce”和“refine”。
    verbose：链是否应在verbose模式下运行。请注意，这适用于组成最终链的所有链。
"""
chain = load_summarize_chain(llm, chain_type="map_reduce", verbose=True)

print(chain.run(texts))
