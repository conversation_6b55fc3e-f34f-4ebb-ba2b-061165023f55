# 第一步：下载安装langchain
# !pip install -q langchain
import os
from langchain.chat_models import QianfanChatEndpoint
# 人类信息、系统信息、AI信息
from langchain.schema import HumanMessage, SystemMessage, AIMessage

# qianfan sdk 鉴权
os.environ["QIANFAN_AK"] = "7Ipu16Ub12oICOLXLG9VLZDk"
os.environ["QIANFAN_SK"] = "Qo8YVuvgjyheMFpsehlBA5EFghiXY16E"

chat = QianfanChatEndpoint(model="ERNIE-Bot-turbo", temperature=0.5)
_out_ = chat(
    [
        SystemMessage(
            content="你是一个鲁菜点餐机器人，帮助用户知道该吃什么。"
        ),
        HumanMessage(
            content="我喜欢吃西红柿，我该吃什么？"
        )
       
    ]
)

print(_out_)


_out_ = chat(
    [
        SystemMessage(
            content="你是一个鲁菜点餐机器人，帮助用户知道该吃什么。"
        ),
        HumanMessage(
            content="我喜欢吃西红柿，我该吃什么？"
        ),
        AIMessage(
            content="你应该吃西红柿炒鸡蛋"
        ),
        HumanMessage(
            content="我该如何做这道菜？"
        )
    ]
)

print(_out_)
