# Python变量类型说明

## 概述

Python中的变量类型与Java有相似之处，但也有重要区别。让我详细分析项目中使用的各种变量类型。

## 1. 类变量（Class Variables）- 类似Java静态变量

### 示例：PromptConfig.ENV_CONFIGS

```python
class PromptConfig:
    # 这是类变量，类似Java的static变量
    ENV_CONFIGS = {
        'ysprod': {...},
        'development': {...}
    }
```

**特点：**
- ✅ 所有实例共享同一个变量
- ✅ 可以通过类名直接访问：`PromptConfig.ENV_CONFIGS`
- ✅ 在类加载时初始化，程序运行期间保持不变
- ✅ 类似Java的 `static` 变量

## 2. 模块级变量（Module Variables）- 类似Java静态变量

### 示例：config_env.py中的变量

```python
# config_env.py
_current_env = None  # 模块级私有变量
env_name = get_env_name()  # 模块级公共变量
```

**特点：**
- ✅ 整个模块共享
- ✅ 第一次导入模块时初始化
- ✅ 在程序运行期间保持状态
- ✅ 类似Java的 `static` 变量

## 3. 类方法（Class Methods）- 类似Java静态方法

### 示例：@classmethod装饰的方法

```python
class PromptConfig:
    @classmethod
    def get_current_config(cls):
        # 这是类方法，类似Java的static方法
        return cls.ENV_CONFIGS.get(current_env, cls.ENV_CONFIGS['development'])
```

**特点：**
- ✅ 可以通过类名直接调用：`PromptConfig.get_current_config()`
- ✅ 不需要创建实例
- ✅ 类似Java的 `static` 方法

## 4. 全局变量与函数

### 示例：config_env.py中的函数

```python
def get_env_name():
    global _current_env
    # 函数内部可以修改全局变量
    return _current_env or 'ysprod'

def set_env_name(env):
    global _current_env
    _current_env = env
```

**特点：**
- ✅ 函数类似Java的 `static` 方法
- ✅ 全局变量类似Java的 `static` 变量
- ⚠️ 需要使用 `global` 关键字修改全局变量

## 5. 与Java的对比

| Python | Java | 说明 |
|--------|------|------|
| 类变量 | static 变量 | 类级别共享 |
| 模块变量 | static 变量 | 模块级别共享 |
| @classmethod | static 方法 | 类级别方法 |
| 模块函数 | static 方法 | 无需实例调用 |
| global变量 | static 变量 | 全局共享状态 |

## 6. 项目中的实际应用

### 配置管理
```python
# 类似Java的静态配置类
class PromptConfig:
    ENV_CONFIGS = {...}  # 静态配置数据
    
    @classmethod
    def get_template(cls):  # 静态方法
        return cls.ENV_CONFIGS[env]
```

### 环境状态管理
```python
# 类似Java的静态工具类
_current_env = None  # 静态状态变量

def get_env_name():  # 静态方法
    return _current_env or 'ysprod'

def set_env_name(env):  # 静态方法
    global _current_env
    _current_env = env
```

## 7. 重要区别

### Python特有特性：
1. **动态性**：可以在运行时修改类变量
2. **模块导入**：变量在模块首次导入时初始化
3. **global关键字**：修改全局变量需要声明

### Java特有特性：
1. **编译时确定**：静态变量在编译时确定
2. **类加载**：静态变量在类加载时初始化
3. **final修饰**：可以声明不可变的静态变量

## 8. 最佳实践

### 在我们的项目中：

1. **配置数据**：使用类变量存储（如ENV_CONFIGS）
2. **工具方法**：使用@classmethod或模块函数
3. **状态管理**：使用模块级变量（如_current_env）
4. **命名约定**：
   - 私有变量：`_current_env`（下划线开头）
   - 公共变量：`env_name`
   - 常量：`ENV_CONFIGS`（全大写）

## 9. 线程安全性

⚠️ **注意**：Python的全局变量和类变量在多线程环境下不是自动线程安全的，如果需要线程安全，需要使用锁机制。

```python
import threading

_lock = threading.Lock()
_current_env = None

def set_env_name(env):
    global _current_env
    with _lock:
        _current_env = env
```

## 总结

在我们的项目中，大部分变量确实类似Java的静态变量：
- `ENV_CONFIGS`：类静态变量
- `_current_env`：模块静态变量  
- `get_env_name()`：静态方法
- `@classmethod`：类静态方法

这种设计确保了配置的全局一致性和易于访问性。
