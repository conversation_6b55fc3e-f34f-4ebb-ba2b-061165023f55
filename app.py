from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from server.api.api import api_router
from starlette.middleware.cors import CORSMiddleware
import logging
from server.init_milvus import init_milvus_collections


def createApp():
    app = FastAPI(title="大模型Agent应用开发")
    # set middleware
    # register_middleware(app)
    # api router
    app.include_router(api_router, prefix="/api/v1")
    # set socketio
    # app.mount('/', socket_app)
    # set static files
    # app.mount("/media", StaticFiles(directory="media"), name="media")   # 媒体文件
    # allow cross domain
    app.add_middleware(CORSMiddleware, allow_origins=['*'],
                       allow_credentials=True, allow_methods=["*"], allow_headers=["*"])

    # # print all path
    # for _route in app.routes:
    #     r = _route.__dict__
    #     print(r['path'], r.get('methods', {}))

    # 初始化Milvus集合
    try:
        init_milvus_collections()
    except Exception as e:
        logging.error(f"初始化Milvus集合失败: {e}")
        # 记录错误但不中断应用启动

    return app



app = createApp()


if __name__ == '__main__':
    import uvicorn
    # Don't set debug/reload equals True in release, because TimedRotatingFileHandler can't support multi-prcoess
    # please used "uvicorn --host 127.0.0.1 --port 8000 main:app --env-file ./configs/.env" run in release, and used "python main.py" in dev
    uvicorn.run(
        app=app,
        host=str('0.0.0.0'),
        port=8089,
        reload=False
    )


"""
Celery schedule worker

1) start worker in project base path

    celery -A workers  worker -l info

2) start beat in project base path

    celery -A workers beat -l info

"""