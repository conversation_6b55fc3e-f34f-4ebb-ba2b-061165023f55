@echo off
echo ===== 大模型Agent应用系统启动脚本 =====
echo.

REM 登录阿里云Docker Registry
echo 正在登录阿里云Docker Registry...
docker login --username "鲁中煤炭交易中心123" --password "鲁中煤炭交易中心123" registry.cn-hangzhou.aliyuncs.com

REM 检查登录状态
if %ERRORLEVEL% NEQ 0 (
    echo Docker登录失败，请检查账号密码是否正确！
    pause
    exit /b 1
)
echo Docker登录成功！
echo.

REM 检查并停止已存在的容器
echo 检查是否存在旧版容器...
docker ps -a | findstr cmks-llm > nul
if %ERRORLEVEL% EQU 0 (
    echo 发现旧版容器，正在停止并删除...
    docker rm -f cmks-llm
    if %ERRORLEVEL% NEQ 0 (
        echo 删除旧容器失败！
        pause
        exit /b 1
    )
    echo 旧版容器已成功删除！
) else (
    echo 未发现旧版容器，继续执行...
)
echo.

REM 拉取最新镜像
echo 正在拉取最新镜像...
docker pull registry.cn-hangzhou.aliyuncs.com/szyktest1/szyk-cmks-llm-ysrpod:1.00
if %ERRORLEVEL% NEQ 0 (
    echo 拉取镜像失败！
    pause
    exit /b 1
)
echo 镜像拉取成功！
echo.

REM 启动新容器
echo 正在启动容器...
docker run -d -p 8089:8089 -e profile=ysprod --name cmks-llm registry.cn-hangzhou.aliyuncs.com/szyktest1/szyk-cmks-llm-ysrpod:1.00
if %ERRORLEVEL% NEQ 0 (
    echo 启动容器失败！
    pause
    exit /b 1
)
echo 容器启动成功！
echo.

REM 显示容器状态
echo 容器状态：
docker ps | findstr cmks-llm

echo.
echo ===== 系统已成功启动 =====
echo 服务地址: http://localhost:8089
echo API文档: http://localhost:8089/docs
echo.
echo 按任意键退出...
pause > nul
