#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile --index-url=https://pypi.tuna.tsinghua.edu.cn/simple requirements.in
#
--index-url https://pypi.tuna.tsinghua.edu.cn/simple

aiohttp==3.9.1
    # via
    #   -r requirements.in
    #   langchain
    #   langchain-community
    #   qianfan
aiolimiter==1.1.0
    # via
    #   -r requirements.in
    #   qianfan
aiosignal==1.3.1
    # via
    #   -r requirements.in
    #   aiohttp
annotated-types==0.6.0
    # via
    #   -r requirements.in
    #   pydantic
anyio==4.2.0
    # via
    #   -r requirements.in
    #   httpx
    #   openai
    #   starlette
async-timeout==4.0.3
    # via -r requirements.in
attrs==23.2.0
    # via
    #   -r requirements.in
    #   aiohttp
backoff==2.2.1
    # via unstructured
bce-python-sdk==0.9.2
    # via
    #   -r requirements.in
    #   qianfan
beautifulsoup4==4.12.3
    # via
    #   -r requirements.in
    #   unstructured
cachetools==5.5.1
    # via qianfan
certifi==2023.11.17
    # via
    #   -r requirements.in
    #   httpcore
    #   httpx
    #   requests
    #   unstructured-client
chardet==5.2.0
    # via unstructured
charset-normalizer==3.3.2
    # via
    #   -r requirements.in
    #   requests
    #   unstructured-client
click==8.1.7
    # via
    #   -r requirements.in
    #   nltk
    #   typer
    #   uvicorn
colorama==0.4.6
    # via
    #   click
    #   tqdm
dataclasses-json==0.6.3
    # via
    #   -r requirements.in
    #   langchain-community
    #   unstructured
dataclasses-json-speakeasy==0.5.11
    # via unstructured-client
dbutils==3.1.0
    # via -r requirements.in
dill==0.3.9
    # via multiprocess
diskcache==5.6.3
    # via qianfan
distro==1.9.0
    # via openai
docx2txt==0.8
    # via -r requirements.in
emoji==2.14.1
    # via unstructured
et-xmlfile==2.0.0
    # via openpyxl
exceptiongroup==1.2.0
    # via -r requirements.in
fastapi==0.109.1
    # via -r requirements.in
filetype==1.2.0
    # via unstructured
frozenlist==1.4.1
    # via
    #   -r requirements.in
    #   aiohttp
    #   aiosignal
future==0.18.3
    # via
    #   -r requirements.in
    #   bce-python-sdk
greenlet==3.1.1
    # via sqlalchemy
grpcio==1.67.1
    # via pymilvus
h11==0.14.0
    # via
    #   -r requirements.in
    #   httpcore
    #   uvicorn
httpcore==1.0.7
    # via httpx
httpx==0.28.1
    # via
    #   langsmith
    #   openai
idna==3.6
    # via
    #   -r requirements.in
    #   anyio
    #   httpx
    #   requests
    #   unstructured-client
    #   yarl
jiter==0.8.2
    # via openai
joblib==1.4.2
    # via nltk
jsonpatch==1.33
    # via
    #   -r requirements.in
    #   langchain-core
jsonpath-python==1.0.6
    # via unstructured-client
jsonpointer==2.4
    # via
    #   -r requirements.in
    #   jsonpatch
langchain==0.3.0
    # via
    #   -r requirements.in
    #   langchain-community
langchain-community==0.3.0
    # via -r requirements.in
langchain-core==0.3.34
    # via
    #   -r requirements.in
    #   langchain
    #   langchain-community
    #   langchain-openai
    #   langchain-text-splitters
    #   langgraph
    #   langgraph-checkpoint
langchain-openai==0.3.0
    # via -r requirements.in
langchain-text-splitters==0.3.6
    # via langchain
langdetect==1.0.9
    # via unstructured
langgraph==0.2.20
    # via -r requirements.in
langgraph-checkpoint==1.0.12
    # via langgraph
langsmith==0.1.147
    # via
    #   langchain
    #   langchain-community
    #   langchain-core
lxml==5.3.1
    # via unstructured
markdown-it-py==3.0.0
    # via
    #   -r requirements.in
    #   rich
marshmallow==3.20.2
    # via
    #   -r requirements.in
    #   dataclasses-json
    #   dataclasses-json-speakeasy
    #   unstructured-client
mdurl==0.1.2
    # via
    #   -r requirements.in
    #   markdown-it-py
msgpack==1.1.0
    # via langgraph-checkpoint
multidict==6.0.4
    # via
    #   -r requirements.in
    #   aiohttp
    #   yarl
multiprocess==0.70.17
    # via qianfan
mypy-extensions==1.0.0
    # via
    #   -r requirements.in
    #   typing-inspect
    #   unstructured-client
nltk==3.9.1
    # via unstructured
numpy==1.24.4
    # via
    #   -r requirements.in
    #   langchain
    #   langchain-community
    #   pandas
    #   pyarrow
    #   unstructured
openai==1.61.1
    # via langchain-openai
openpyxl==3.1.5
    # via -r requirements.in
orjson==3.10.15
    # via langsmith
packaging==23.2
    # via
    #   -r requirements.in
    #   langchain-core
    #   marshmallow
    #   unstructured-client
pandas==2.0.3
    # via
    #   -r requirements.in
    #   pymilvus
prompt-toolkit==3.0.43
    # via
    #   -r requirements.in
    #   qianfan
proto-plus==1.22.3
    # via -r requirements.in
protobuf==4.25.2
    # via
    #   -r requirements.in
    #   proto-plus
    #   pymilvus
pyarrow==15.0.0
    # via -r requirements.in
pycryptodome==3.20.0
    # via
    #   -r requirements.in
    #   bce-python-sdk
pydantic===2.7.4
    # via
    #   -r requirements.in
    #   fastapi
    #   langchain
    #   openai
    #   pydantic-settings
    #   qianfan
pydantic-core==2.18.4
    # via
    #   -r requirements.in
    #   pydantic
pydantic-settings==2.7.1
    # via langchain-community
pygments==2.17.2
    # via
    #   -r requirements.in
    #   rich
pymilvus==2.5.0
    # via -r requirements.in
pymysql==1.1.0
    # via -r requirements.in
pypdf==4.1.0
    # via -r requirements.in
python-dateutil==2.8.2
    # via
    #   -r requirements.in
    #   pandas
    #   unstructured-client
python-dotenv==1.0.1
    # via
    #   -r requirements.in
    #   pydantic-settings
    #   pymilvus
    #   qianfan
python-iso639==2025.2.8
    # via unstructured
python-magic==0.4.27
    # via unstructured
pytz==2025.1
    # via pandas
pyyaml==6.0.1
    # via
    #   -r requirements.in
    #   langchain
    #   langchain-community
    #   langchain-core
    #   qianfan
qianfan==0.4.12.2
    # via -r requirements.in
rapidfuzz==3.12.1
    # via unstructured
redis==5.0.0
    # via -r requirements.in
regex==2024.11.6
    # via
    #   nltk
    #   tiktoken
requests==2.31.0
    # via
    #   -r requirements.in
    #   langchain
    #   langchain-community
    #   langsmith
    #   qianfan
    #   requests-toolbelt
    #   tiktoken
    #   unstructured
    #   unstructured-client
requests-toolbelt==1.0.0
    # via langsmith
rich==13.7.0
    # via
    #   -r requirements.in
    #   qianfan
six==1.16.0
    # via
    #   -r requirements.in
    #   bce-python-sdk
    #   langdetect
    #   python-dateutil
    #   unstructured-client
sniffio==1.3.0
    # via
    #   -r requirements.in
    #   anyio
    #   openai
soupsieve==2.6
    # via beautifulsoup4
sqlalchemy==1.4.51
    # via
    #   -r requirements.in
    #   langchain
    #   langchain-community
sqlglot==22.4.0
    # via -r requirements.in
starlette==0.35.1
    # via
    #   -r requirements.in
    #   fastapi
tabulate==0.9.0
    # via unstructured
tenacity==8.2.3
    # via
    #   -r requirements.in
    #   langchain
    #   langchain-community
    #   langchain-core
    #   qianfan
tiktoken==0.8.0
    # via langchain-openai
tqdm==4.67.1
    # via
    #   nltk
    #   openai
typer==0.9.0
    # via
    #   -r requirements.in
    #   qianfan
typing-extensions==4.12.2
    # via
    #   -r requirements.in
    #   fastapi
    #   langchain-core
    #   openai
    #   pydantic
    #   pydantic-core
    #   typer
    #   typing-inspect
    #   unstructured
    #   unstructured-client
typing-inspect==0.9.0
    # via
    #   -r requirements.in
    #   dataclasses-json
    #   dataclasses-json-speakeasy
    #   unstructured-client
tzdata==2025.1
    # via pandas
ujson==5.10.0
    # via pymilvus
unstructured==0.14.3
    # via -r requirements.in
unstructured-client==0.18.0
    # via unstructured
urllib3==1.26.18
    # via
    #   -r requirements.in
    #   requests
    #   unstructured-client
uvicorn==0.26.0
    # via -r requirements.in
wcwidth==0.2.13
    # via
    #   -r requirements.in
    #   prompt-toolkit
wheel==0.42.0
    # via -r requirements.in
wrapt==1.17.2
    # via unstructured
yarl==1.9.4
    # via
    #   -r requirements.in
    #   aiohttp

# The following packages are considered to be unsafe in a requirements file:
# pip
# setuptools
